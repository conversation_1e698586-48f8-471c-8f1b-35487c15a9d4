-- 技术知识管理数据库设计
-- 用于存储AI助手提供的技术资料、代码示例和演示

CREATE DATABASE IF NOT EXISTS tech_knowledge_base CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tech_knowledge_base;

-- 1. 知识分类表
CREATE TABLE knowledge_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES knowledge_categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id)
);

-- 2. 知识条目主表
CREATE TABLE knowledge_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    summary TEXT,
    content LONGTEXT NOT NULL,
    category_id INT,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'intermediate',
    tags JSON,
    source_type ENUM('ai_explanation', 'code_example', 'documentation', 'tutorial') DEFAULT 'ai_explanation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES knowledge_categories(id) ON DELETE SET NULL,
    FULLTEXT KEY ft_content (title, summary, content),
    INDEX idx_category (category_id),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_source_type (source_type)
);

-- 3. 代码示例表
CREATE TABLE code_examples (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    language VARCHAR(50) NOT NULL,
    code_content LONGTEXT NOT NULL,
    file_path VARCHAR(500),
    line_start INT,
    line_end INT,
    is_executable BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_language (language)
);

-- 4. 图表演示表
CREATE TABLE diagrams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    diagram_type ENUM('mermaid', 'flowchart', 'sequence', 'class', 'state') DEFAULT 'mermaid',
    diagram_definition LONGTEXT NOT NULL,
    rendered_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_diagram_type (diagram_type)
);

-- 5. 学习路径表
CREATE TABLE learning_paths (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    estimated_hours INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 6. 学习路径步骤表
CREATE TABLE learning_path_steps (
    id INT PRIMARY KEY AUTO_INCREMENT,
    learning_path_id INT NOT NULL,
    knowledge_entry_id INT NOT NULL,
    step_order INT NOT NULL,
    is_required BOOLEAN DEFAULT TRUE,
    notes TEXT,
    FOREIGN KEY (learning_path_id) REFERENCES learning_paths(id) ON DELETE CASCADE,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_path_step (learning_path_id, step_order),
    INDEX idx_learning_path (learning_path_id),
    INDEX idx_knowledge_entry (knowledge_entry_id)
);

-- 7. 相关链接表
CREATE TABLE related_links (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    link_type ENUM('documentation', 'tutorial', 'example', 'reference') DEFAULT 'reference',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    INDEX idx_knowledge_entry (knowledge_entry_id)
);

-- 8. 搜索历史表
CREATE TABLE search_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    search_query VARCHAR(500) NOT NULL,
    search_type ENUM('fulltext', 'tag', 'category', 'code') DEFAULT 'fulltext',
    results_count INT DEFAULT 0,
    user_session VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_search_query (search_query),
    INDEX idx_user_session (user_session),
    INDEX idx_created_at (created_at)
);

-- 插入基础分类数据
INSERT INTO knowledge_categories (name, description, parent_id) VALUES
('Metal编程', 'Apple Metal图形编程相关知识', NULL),
('3D图形学', '3D图形学基础概念和技术', NULL),
('Swift开发', 'Swift语言和iOS/macOS开发', NULL),
('数据库设计', '数据库设计和优化相关知识', NULL);

INSERT INTO knowledge_categories (name, description, parent_id) VALUES
('坐标系统', 'Metal中的坐标系统和变换', 1),
('深度测试', '深度缓冲和深度测试机制', 1),
('着色器编程', 'Metal着色器语言(MSL)编程', 1),
('渲染管线', 'Metal渲染管线和状态管理', 1);

-- 简化的多媒体知识管理数据库扩展
USE tech_knowledge_base;

-- 1. 媒体文件表
CREATE TABLE IF NOT EXISTS media_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    media_type ENUM('audio', 'video', 'image', 'document') NOT NULL,
    duration_seconds INT DEFAULT NULL,
    file_hash VARCHAR(64) NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON,
    INDEX idx_media_type (media_type),
    INDEX idx_file_hash (file_hash)
);

-- 2. 语音转文字表
CREATE TABLE IF NOT EXISTS speech_transcriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    media_file_id INT NOT NULL,
    transcription_text LONGTEXT NOT NULL,
    confidence_score DECIMAL(3,2),
    language VARCHAR(10) DEFAULT 'zh-CN',
    transcription_engine VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    INDEX idx_media_file (media_file_id)
);

-- 3. 知识条目媒体关联表
CREATE TABLE IF NOT EXISTS knowledge_media (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    media_file_id INT NOT NULL,
    media_role ENUM('main_audio', 'explanation_audio', 'demo_video', 'illustration', 'attachment') NOT NULL,
    description TEXT,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_media_file (media_file_id)
);

-- 4. 语音注释表
CREATE TABLE IF NOT EXISTS audio_annotations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    media_file_id INT NOT NULL,
    start_time DECIMAL(8,3) NOT NULL,
    end_time DECIMAL(8,3) NOT NULL,
    annotation_text TEXT NOT NULL,
    annotation_type ENUM('note', 'highlight', 'question', 'summary') DEFAULT 'note',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    INDEX idx_media_file (media_file_id)
);

-- 5. 多维度搜索索引表
CREATE TABLE IF NOT EXISTS knowledge_search_index (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    content_type ENUM('text', 'audio_transcript', 'image_ocr', 'video_caption') NOT NULL,
    searchable_content LONGTEXT NOT NULL,
    content_weight DECIMAL(3,2) DEFAULT 1.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_content_type (content_type)
);

-- 插入示例数据
INSERT IGNORE INTO media_files (filename, original_name, file_path, file_size, mime_type, media_type, duration_seconds, file_hash, metadata) VALUES
('demo_audio.mp3', 'Metal深度测试解释.mp3', 'media/audio/demo_audio.mp3', 1024000, 'audio/mpeg', 'audio', 45, 'demo_hash_123', '{"sample_rate": 44100, "channels": 2}');

-- 获取刚插入的媒体文件ID和现有知识条目ID
SET @media_id = LAST_INSERT_ID();
SET @knowledge_id = (SELECT id FROM knowledge_entries WHERE title LIKE '%深度测试%' LIMIT 1);

-- 如果找到了知识条目，则创建关联
INSERT IGNORE INTO knowledge_media (knowledge_entry_id, media_file_id, media_role, description) 
SELECT @knowledge_id, @media_id, 'explanation_audio', 'AI助手关于深度测试的语音解释'
WHERE @knowledge_id IS NOT NULL AND @media_id > 0;

-- 插入语音转录
INSERT IGNORE INTO speech_transcriptions (media_file_id, transcription_text, confidence_score, language, transcription_engine)
SELECT @media_id, '深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。Metal使用深度缓冲区来存储每个像素的深度值，通过比较新像素和已存储像素的深度值来决定是否渲染。这种机制确保了正确的3D视觉效果，让近处的对象能够正确遮挡远处的对象。', 0.95, 'zh-CN', 'demo_engine'
WHERE @media_id > 0;

-- 插入音频注释
INSERT IGNORE INTO audio_annotations (media_file_id, start_time, end_time, annotation_text, annotation_type)
SELECT @media_id, 5.0, 15.0, '深度测试的基本概念介绍', 'highlight'
WHERE @media_id > 0
UNION ALL
SELECT @media_id, 20.0, 30.0, 'Metal中的具体实现方法', 'note'
WHERE @media_id > 0
UNION ALL
SELECT @media_id, 35.0, 45.0, '性能优化技巧', 'summary'
WHERE @media_id > 0;

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白名单参数控制系统 - 只允许预定义的参数和用途
体现"设计好的用途"安全理念
"""

from typing import Dict, List, Any, Optional
import json
from datetime import datetime

class WhitelistController:
    """白名单控制器 - 只允许预定义的参数组合"""
    
    def __init__(self):
        # 每个API端点的严格参数定义
        self.api_definitions = {
            # 知识搜索API - 只能搜索，不能做其他事
            "knowledge_search": {
                "allowed_params": ["query", "category", "difficulty", "page", "limit"],
                "required_params": ["query"],
                "param_types": {
                    "query": str,
                    "category": str, 
                    "difficulty": str,
                    "page": int,
                    "limit": int
                },
                "param_constraints": {
                    "query": {"max_length": 100, "min_length": 1},
                    "category": {"allowed_values": ["Metal编程", "Swift开发", "3D图形学"]},
                    "difficulty": {"allowed_values": ["beginner", "intermediate", "advanced"]},
                    "page": {"min_value": 1, "max_value": 100},
                    "limit": {"min_value": 1, "max_value": 50}
                },
                "purpose": "搜索已有知识内容"
            },
            
            # 知识创建API - 只能创建，参数严格限定
            "knowledge_create": {
                "allowed_params": ["title", "content", "summary", "category", "tags", "difficulty"],
                "required_params": ["title", "content"],
                "param_types": {
                    "title": str,
                    "content": str,
                    "summary": str,
                    "category": str,
                    "tags": list,
                    "difficulty": str
                },
                "param_constraints": {
                    "title": {"max_length": 200, "min_length": 5},
                    "content": {"max_length": 10000, "min_length": 10},
                    "summary": {"max_length": 500},
                    "category": {"allowed_values": ["Metal编程", "Swift开发", "3D图形学"]},
                    "difficulty": {"allowed_values": ["beginner", "intermediate", "advanced"]},
                    "tags": {"max_items": 10}
                },
                "purpose": "创建新的知识条目"
            },
            
            # 用户认证API - 只能登录验证
            "user_login": {
                "allowed_params": ["username", "password"],
                "required_params": ["username", "password"],
                "param_types": {
                    "username": str,
                    "password": str
                },
                "param_constraints": {
                    "username": {"max_length": 50, "min_length": 3, "pattern": "^[a-zA-Z0-9_]+$"},
                    "password": {"max_length": 100, "min_length": 6}
                },
                "purpose": "用户身份验证"
            }
        }
    
    def validate_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证请求是否符合预定义用途"""
        print(f"\n🔍 验证端点: {endpoint}")
        print(f"📋 预定义用途: {self.api_definitions.get(endpoint, {}).get('purpose', '未知')}")
        
        # 1. 检查端点是否存在
        if endpoint not in self.api_definitions:
            return {
                "valid": False,
                "error": f"未定义的端点: {endpoint}",
                "reason": "不在白名单中"
            }
        
        definition = self.api_definitions[endpoint]
        
        # 2. 检查是否有多余参数
        extra_params = set(params.keys()) - set(definition["allowed_params"])
        if extra_params:
            return {
                "valid": False,
                "error": f"不允许的参数: {list(extra_params)}",
                "reason": "包含预定义用途之外的参数"
            }
        
        # 3. 检查必需参数
        missing_params = set(definition["required_params"]) - set(params.keys())
        if missing_params:
            return {
                "valid": False,
                "error": f"缺少必需参数: {list(missing_params)}",
                "reason": "不满足预定义用途的最小要求"
            }
        
        # 4. 验证参数类型和约束
        validation_result = self._validate_param_constraints(params, definition)
        if not validation_result["valid"]:
            return validation_result
        
        # 5. 返回清理后的参数
        clean_params = self._clean_params(params, definition)
        
        return {
            "valid": True,
            "clean_params": clean_params,
            "purpose": definition["purpose"]
        }
    
    def _validate_param_constraints(self, params: Dict, definition: Dict) -> Dict:
        """验证参数约束"""
        constraints = definition.get("param_constraints", {})
        
        for param_name, param_value in params.items():
            if param_name in constraints:
                constraint = constraints[param_name]
                
                # 检查类型
                expected_type = definition["param_types"].get(param_name)
                if expected_type and not isinstance(param_value, expected_type):
                    return {
                        "valid": False,
                        "error": f"参数 {param_name} 类型错误，期望 {expected_type.__name__}",
                        "reason": "参数类型不符合预定义"
                    }
                
                # 检查字符串长度
                if isinstance(param_value, str):
                    if "max_length" in constraint and len(param_value) > constraint["max_length"]:
                        return {
                            "valid": False,
                            "error": f"参数 {param_name} 长度超限",
                            "reason": "参数值超出预定义范围"
                        }
                    
                    if "min_length" in constraint and len(param_value) < constraint["min_length"]:
                        return {
                            "valid": False,
                            "error": f"参数 {param_name} 长度不足",
                            "reason": "参数值不满足最小要求"
                        }
                    
                    # 检查允许的值
                    if "allowed_values" in constraint and param_value not in constraint["allowed_values"]:
                        return {
                            "valid": False,
                            "error": f"参数 {param_name} 值不在允许范围内",
                            "reason": "参数值不在预定义列表中"
                        }
                
                # 检查数值范围
                if isinstance(param_value, int):
                    if "min_value" in constraint and param_value < constraint["min_value"]:
                        return {
                            "valid": False,
                            "error": f"参数 {param_name} 值过小",
                            "reason": "数值超出预定义下限"
                        }
                    
                    if "max_value" in constraint and param_value > constraint["max_value"]:
                        return {
                            "valid": False,
                            "error": f"参数 {param_name} 值过大", 
                            "reason": "数值超出预定义上限"
                        }
        
        return {"valid": True}
    
    def _clean_params(self, params: Dict, definition: Dict) -> Dict:
        """清理参数，只保留允许的部分"""
        clean_params = {}
        
        for param_name in definition["allowed_params"]:
            if param_name in params:
                value = params[param_name]
                
                # 字符串清理
                if isinstance(value, str):
                    # 移除前后空白
                    value = value.strip()
                    
                    # 长度限制
                    constraints = definition.get("param_constraints", {}).get(param_name, {})
                    max_len = constraints.get("max_length")
                    if max_len and len(value) > max_len:
                        value = value[:max_len]
                
                clean_params[param_name] = value
        
        return clean_params


def demonstrate_whitelist_security():
    """演示白名单安全控制"""
    print("🛡️  白名单参数控制演示")
    print("核心理念: 只允许预定义用途的参数组合\n")
    
    controller = WhitelistController()
    
    # 测试用例
    test_cases = [
        {
            "name": "✅ 正常搜索请求",
            "endpoint": "knowledge_search",
            "params": {
                "query": "Metal坐标系",
                "category": "Metal编程",
                "page": 1,
                "limit": 10
            }
        },
        {
            "name": "❌ 尝试添加额外参数",
            "endpoint": "knowledge_search", 
            "params": {
                "query": "Metal坐标系",
                "admin_override": True,  # 恶意参数
                "delete_all": "yes"      # 恶意参数
            }
        },
        {
            "name": "❌ 参数值超出范围",
            "endpoint": "knowledge_search",
            "params": {
                "query": "Metal坐标系",
                "category": "黑客技术",  # 不在允许列表中
                "limit": 1000           # 超出最大值限制
            }
        },
        {
            "name": "❌ 访问未定义端点",
            "endpoint": "admin_delete_all",  # 危险端点
            "params": {
                "confirm": "yes"
            }
        },
        {
            "name": "✅ 正常创建知识",
            "endpoint": "knowledge_create",
            "params": {
                "title": "Metal深度测试",
                "content": "详细介绍Metal中的深度测试机制...",
                "category": "Metal编程",
                "difficulty": "intermediate"
            }
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"📋 测试案例 {i}: {case['name']}")
        print("-" * 60)
        
        result = controller.validate_request(case["endpoint"], case["params"])
        
        if result["valid"]:
            print(f"✅ 请求通过验证")
            print(f"🎯 用途: {result['purpose']}")
            print(f"🧹 清理后参数: {json.dumps(result['clean_params'], ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求被拒绝")
            print(f"📝 错误: {result['error']}")
            print(f"🔍 原因: {result['reason']}")
        
        print()


if __name__ == "__main__":
    demonstrate_whitelist_security()
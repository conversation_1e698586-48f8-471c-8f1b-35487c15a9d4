#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术知识管理系统
用于保存、搜索和展示AI助手提供的技术资料
"""

import mysql.connector
import json
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import hashlib

class KnowledgeManager:
    def __init__(self, host='localhost', user='root', password='', database='tech_knowledge_base'):
        """初始化知识管理器"""
        self.connection = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True
        )
        self.cursor = self.connection.cursor(dictionary=True)
    
    def save_knowledge_entry(self, title: str, content: str, summary: str = "", 
                           category_name: str = "", tags: List[str] = None,
                           difficulty: str = "intermediate", source_type: str = "ai_explanation") -> int:
        """保存知识条目"""
        if tags is None:
            tags = []
        
        # 获取分类ID
        category_id = self._get_or_create_category(category_name) if category_name else None
        
        # 插入知识条目
        query = """
        INSERT INTO knowledge_entries 
        (title, summary, content, category_id, difficulty_level, tags, source_type)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        self.cursor.execute(query, (
            title, summary, content, category_id, difficulty, 
            json.dumps(tags, ensure_ascii=False), source_type
        ))
        
        return self.cursor.lastrowid
    
    def save_code_example(self, knowledge_entry_id: int, title: str, 
                         language: str, code_content: str, description: str = "",
                         file_path: str = "", line_start: int = None, line_end: int = None) -> int:
        """保存代码示例"""
        query = """
        INSERT INTO code_examples 
        (knowledge_entry_id, title, description, language, code_content, file_path, line_start, line_end)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        self.cursor.execute(query, (
            knowledge_entry_id, title, description, language, 
            code_content, file_path, line_start, line_end
        ))
        
        return self.cursor.lastrowid
    
    def save_diagram(self, knowledge_entry_id: int, title: str, 
                    diagram_definition: str, diagram_type: str = "mermaid",
                    description: str = "") -> int:
        """保存图表演示"""
        query = """
        INSERT INTO diagrams 
        (knowledge_entry_id, title, description, diagram_type, diagram_definition)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        self.cursor.execute(query, (
            knowledge_entry_id, title, description, diagram_type, diagram_definition
        ))
        
        return self.cursor.lastrowid
    
    def search_knowledge(self, query: str, search_type: str = "fulltext", 
                        category: str = "", tags: List[str] = None) -> List[Dict]:
        """搜索知识条目"""
        if tags is None:
            tags = []
        
        # 记录搜索历史
        self._record_search(query, search_type)
        
        if search_type == "fulltext":
            return self._fulltext_search(query, category)
        elif search_type == "tag":
            return self._tag_search(tags)
        elif search_type == "category":
            return self._category_search(category)
        elif search_type == "code":
            return self._code_search(query)
        else:
            return []
    
    def get_knowledge_with_examples(self, knowledge_id: int) -> Dict:
        """获取完整的知识条目（包含代码示例和图表）"""
        # 获取主要知识条目
        query = """
        SELECT ke.*, kc.name as category_name 
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE ke.id = %s
        """
        self.cursor.execute(query, (knowledge_id,))
        entry = self.cursor.fetchone()
        
        if not entry:
            return {}
        
        # 获取代码示例
        query = "SELECT * FROM code_examples WHERE knowledge_entry_id = %s ORDER BY id"
        self.cursor.execute(query, (knowledge_id,))
        entry['code_examples'] = self.cursor.fetchall()
        
        # 获取图表
        query = "SELECT * FROM diagrams WHERE knowledge_entry_id = %s ORDER BY id"
        self.cursor.execute(query, (knowledge_id,))
        entry['diagrams'] = self.cursor.fetchall()
        
        # 获取相关链接
        query = "SELECT * FROM related_links WHERE knowledge_entry_id = %s ORDER BY id"
        self.cursor.execute(query, (knowledge_id,))
        entry['related_links'] = self.cursor.fetchall()
        
        return entry
    
    def create_learning_path(self, name: str, description: str, 
                           knowledge_ids: List[int], difficulty: str = "beginner") -> int:
        """创建学习路径"""
        # 插入学习路径
        query = """
        INSERT INTO learning_paths (name, description, difficulty_level)
        VALUES (%s, %s, %s)
        """
        self.cursor.execute(query, (name, description, difficulty))
        path_id = self.cursor.lastrowid
        
        # 插入学习步骤
        for order, knowledge_id in enumerate(knowledge_ids, 1):
            query = """
            INSERT INTO learning_path_steps (learning_path_id, knowledge_entry_id, step_order)
            VALUES (%s, %s, %s)
            """
            self.cursor.execute(query, (path_id, knowledge_id, order))
        
        return path_id
    
    def get_learning_path(self, path_id: int) -> Dict:
        """获取学习路径详情"""
        # 获取路径信息
        query = "SELECT * FROM learning_paths WHERE id = %s"
        self.cursor.execute(query, (path_id,))
        path = self.cursor.fetchone()
        
        if not path:
            return {}
        
        # 获取学习步骤
        query = """
        SELECT lps.*, ke.title, ke.summary, ke.difficulty_level
        FROM learning_path_steps lps
        JOIN knowledge_entries ke ON lps.knowledge_entry_id = ke.id
        WHERE lps.learning_path_id = %s
        ORDER BY lps.step_order
        """
        self.cursor.execute(query, (path_id,))
        path['steps'] = self.cursor.fetchall()
        
        return path
    
    def _get_or_create_category(self, category_name: str) -> int:
        """获取或创建分类"""
        query = "SELECT id FROM knowledge_categories WHERE name = %s"
        self.cursor.execute(query, (category_name,))
        result = self.cursor.fetchone()
        
        if result:
            return result['id']
        
        # 创建新分类
        query = "INSERT INTO knowledge_categories (name) VALUES (%s)"
        self.cursor.execute(query, (category_name,))
        return self.cursor.lastrowid
    
    def _fulltext_search(self, query: str, category: str = "") -> List[Dict]:
        """全文搜索"""
        sql = """
        SELECT ke.*, kc.name as category_name,
               MATCH(ke.title, ke.summary, ke.content) AGAINST(%s IN NATURAL LANGUAGE MODE) as relevance
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE MATCH(ke.title, ke.summary, ke.content) AGAINST(%s IN NATURAL LANGUAGE MODE)
        """
        params = [query, query]
        
        if category:
            sql += " AND kc.name = %s"
            params.append(category)
        
        sql += " ORDER BY relevance DESC LIMIT 20"
        
        self.cursor.execute(sql, params)
        return self.cursor.fetchall()
    
    def _tag_search(self, tags: List[str]) -> List[Dict]:
        """标签搜索"""
        if not tags:
            return []
        
        # 构建JSON查询条件
        conditions = []
        params = []
        for tag in tags:
            conditions.append("JSON_CONTAINS(ke.tags, %s)")
            params.append(json.dumps(tag, ensure_ascii=False))
        
        sql = f"""
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE {' AND '.join(conditions)}
        ORDER BY ke.updated_at DESC
        """
        
        self.cursor.execute(sql, params)
        return self.cursor.fetchall()
    
    def _category_search(self, category: str) -> List[Dict]:
        """分类搜索"""
        sql = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE kc.name = %s
        ORDER BY ke.updated_at DESC
        """
        
        self.cursor.execute(sql, (category,))
        return self.cursor.fetchall()
    
    def _code_search(self, query: str) -> List[Dict]:
        """代码搜索"""
        sql = """
        SELECT DISTINCT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        JOIN code_examples ce ON ke.id = ce.knowledge_entry_id
        WHERE ce.code_content LIKE %s OR ce.title LIKE %s OR ce.description LIKE %s
        ORDER BY ke.updated_at DESC
        """
        
        search_pattern = f"%{query}%"
        self.cursor.execute(sql, (search_pattern, search_pattern, search_pattern))
        return self.cursor.fetchall()
    
    def _record_search(self, query: str, search_type: str):
        """记录搜索历史"""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:20]
        sql = """
        INSERT INTO search_history (search_query, search_type, user_session)
        VALUES (%s, %s, %s)
        """
        self.cursor.execute(sql, (query, search_type, query_hash))
    
    def close(self):
        """关闭数据库连接"""
        self.cursor.close()
        self.connection.close()


# 使用示例和测试函数
def save_metal_coordinate_knowledge():
    """保存Metal坐标系知识的示例"""
    km = KnowledgeManager()
    
    # 保存主要知识条目
    knowledge_id = km.save_knowledge_entry(
        title="Metal 3D坐标系详解",
        summary="详细解释Metal中3D坐标系的设计原理，包括坐标变换流程、NDC坐标系特点等",
        content="""Metal 3D坐标系是3D图形渲染的基础概念。在Metal中，坐标系变换遵循标准的3D图形管线：

1. 坐标系变换流程：
   本地坐标 → 世界坐标 → 视图坐标 → 裁剪坐标 → 屏幕坐标

2. NDC (Normalized Device Coordinates) 标准化设备坐标：
   - X轴: -1.0 (左) 到 +1.0 (右)
   - Y轴: -1.0 (下) 到 +1.0 (上)  
   - Z轴: 0.0 (近) 到 1.0 (远)

3. 变换矩阵：
   - 模型矩阵：本地坐标 → 世界坐标
   - 视图矩阵：世界坐标 → 视图坐标
   - 投影矩阵：视图坐标 → 裁剪坐标""",
        category_name="Metal编程",
        tags=["坐标系", "3D变换", "NDC", "变换矩阵"],
        difficulty="intermediate"
    )
    
    # 保存代码示例
    km.save_code_example(
        knowledge_entry_id=knowledge_id,
        title="着色器中的坐标变换",
        language="metal",
        code_content="""vertex VertexOut sphere_vertex(Vertex3D in [[stage_in]],
                              constant Transform& transform [[buffer(1)]],
                              constant float4x4& modelMatrix [[buffer(2)]]) {
    VertexOut out;
    
    // 计算完整的变换矩阵：投影 × 视图 × 世界 × 本地
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    
    // 变换顶点位置
    out.position = mvpMatrix * float4(in.position, 1.0);
    
    return out;
}""",
        description="Metal着色器中实现坐标变换的核心代码"
    )
    
    # 保存图表
    km.save_diagram(
        knowledge_entry_id=knowledge_id,
        title="Metal 3D坐标系变换流程",
        diagram_type="mermaid",
        diagram_definition="""graph TD
    A[本地坐标系<br/>Local Space] --> B[模型矩阵<br/>Model Matrix]
    B --> C[世界坐标系<br/>World Space]
    C --> D[视图矩阵<br/>View Matrix]
    D --> E[视图坐标系<br/>View Space]
    E --> F[投影矩阵<br/>Projection Matrix]
    F --> G[裁剪坐标系<br/>Clip Space]
    G --> H[透视除法<br/>Perspective Division]
    H --> I[标准化设备坐标<br/>NDC Space]
    I --> J[视口变换<br/>Viewport Transform]
    J --> K[屏幕坐标系<br/>Screen Space]""",
        description="展示Metal中3D坐标系的完整变换流程"
    )
    
    print(f"已保存Metal坐标系知识，ID: {knowledge_id}")
    km.close()
    return knowledge_id


if __name__ == "__main__":
    # 测试保存知识
    save_metal_coordinate_knowledge()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面文件夹批量上传工具
支持将整个文件夹（包括子文件夹）上传到知识管理系统
"""

import os
import sys
import requests
import json
import hashlib
import mimetypes
from pathlib import Path
import argparse
from datetime import datetime
import mysql.connector

class FolderUploader:
    def __init__(self, server_url="http://localhost:8081", db_config=None):
        """初始化上传器"""
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()
        
        # 数据库配置
        self.db_config = db_config or {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'tech_knowledge_base',
            'charset': 'utf8mb4'
        }
        
        # 支持的文件类型
        self.supported_extensions = {
            # 代码文件
            'code': {'.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml',
                    '.swift', '.m', '.h', '.cpp', '.c', '.java', '.kt', '.go', '.rs',
                    '.php', '.rb', '.sh', '.sql', '.log', '.md', '.txt'},
            # 多媒体文件
            'media': {'.mp3', '.wav', '.aiff', '.m4a', '.ogg', '.mp4', '.avi', '.mov',
                     '.mkv', '.webm', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'},
            # 文档文件
            'document': {'.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'}
        }
        
        # 忽略的文件和文件夹
        self.ignore_patterns = {
            # 系统文件
            '.DS_Store', 'Thumbs.db', '.gitignore', '.git',
            # 编译产物
            '__pycache__', 'node_modules', '.vscode', '.idea',
            'build', 'dist', 'target', '.gradle',
            # 临时文件
            '.tmp', '.temp', '.cache'
        }
    
    def is_supported_file(self, file_path):
        """检查文件是否支持"""
        file_ext = Path(file_path).suffix.lower()
        
        for category, extensions in self.supported_extensions.items():
            if file_ext in extensions:
                return True
        
        return False
    
    def should_ignore(self, path):
        """检查是否应该忽略此路径"""
        path_name = os.path.basename(path)
        
        # 检查忽略模式
        if path_name in self.ignore_patterns:
            return True
        
        # 检查隐藏文件（以.开头）
        if path_name.startswith('.') and path_name not in {'.gitignore', '.env'}:
            return True
        
        return False
    
    def scan_folder(self, folder_path):
        """扫描文件夹，返回所有支持的文件"""
        files_to_upload = []
        folder_path = Path(folder_path)
        
        print(f"🔍 扫描文件夹: {folder_path}")
        
        for root, dirs, files in os.walk(folder_path):
            # 过滤要忽略的目录
            dirs[:] = [d for d in dirs if not self.should_ignore(os.path.join(root, d))]
            
            for file in files:
                file_path = os.path.join(root, file)
                
                # 检查是否应该忽略
                if self.should_ignore(file_path):
                    continue
                
                # 检查是否支持
                if self.is_supported_file(file_path):
                    # 计算相对路径
                    relative_path = os.path.relpath(file_path, folder_path)
                    files_to_upload.append({
                        'full_path': file_path,
                        'relative_path': relative_path,
                        'size': os.path.getsize(file_path)
                    })
        
        print(f"📊 找到 {len(files_to_upload)} 个支持的文件")
        return files_to_upload
    
    def create_knowledge_entry(self, folder_name, folder_path, file_count):
        """为文件夹创建知识条目"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 创建知识条目
            title = f"项目文件夹: {folder_name}"
            summary = f"包含 {file_count} 个文件的项目文件夹"
            content = f"""# 项目文件夹: {folder_name}

**文件夹路径**: {folder_path}
**上传时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**文件数量**: {file_count} 个文件

## 项目结构

这是一个完整的项目文件夹，包含了开发过程中的各种文件。所有文件都已上传到系统中，可以通过多媒体内容查看。

## 使用说明

1. 查看下方的多媒体内容部分，可以看到所有上传的文件
2. 点击文件可以查看内容或下载
3. 代码文件支持语法高亮显示
4. 可以通过搜索功能查找特定文件

## 文件类型统计

- 代码文件: Python, Swift, JavaScript等
- 文档文件: README, 配置文件等
- 多媒体文件: 图片, 音频, 视频等
"""
            
            query = """
            INSERT INTO knowledge_entries 
            (title, summary, content, category_id, tags, difficulty_level, source_type)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            # 获取或创建"项目文件"分类
            cursor.execute("SELECT id FROM knowledge_categories WHERE name = '项目文件'")
            category = cursor.fetchone()
            
            if not category:
                cursor.execute("INSERT INTO knowledge_categories (name, description) VALUES (%s, %s)",
                             ('项目文件', '开发项目的完整文件夹'))
                category_id = cursor.lastrowid
            else:
                category_id = category['id']
            
            cursor.execute(query, (
                title, summary, content, category_id,
                json.dumps(['项目文件', '批量上传', folder_name], ensure_ascii=False),
                'intermediate', 'folder_upload'
            ))
            
            knowledge_id = cursor.lastrowid
            
            cursor.close()
            conn.close()
            
            print(f"✅ 创建知识条目成功，ID: {knowledge_id}")
            return knowledge_id
            
        except Exception as e:
            print(f"❌ 创建知识条目失败: {e}")
            return None
    
    def upload_file_direct(self, file_info, knowledge_id=None):
        """直接上传文件到数据库（绕过Web接口）"""
        try:
            file_path = file_info['full_path']
            relative_path = file_info['relative_path']
            
            # 计算文件哈希
            with open(file_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            
            # 确定媒体类型
            mime_type, _ = mimetypes.guess_type(file_path)
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext in self.supported_extensions['code']:
                media_type = 'document'
            elif file_ext in self.supported_extensions['media']:
                if mime_type and mime_type.startswith('audio'):
                    media_type = 'audio'
                elif mime_type and mime_type.startswith('video'):
                    media_type = 'video'
                elif mime_type and mime_type.startswith('image'):
                    media_type = 'image'
                else:
                    media_type = 'document'
            else:
                media_type = 'document'
            
            # 创建目标路径
            target_dir = f"media/{media_type}"
            os.makedirs(target_dir, exist_ok=True)
            
            file_ext = Path(file_path).suffix
            target_filename = f"{file_hash}{file_ext}"
            target_path = os.path.join(target_dir, target_filename)
            
            # 复制文件
            import shutil
            if not os.path.exists(target_path):
                shutil.copy2(file_path, target_path)
            
            # 保存到数据库
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 检查是否已存在
            cursor.execute("SELECT id FROM media_files WHERE file_hash = %s", (file_hash,))
            existing = cursor.fetchone()
            
            if existing:
                media_id = existing['id']
            else:
                # 插入新记录
                query = """
                INSERT INTO media_files 
                (filename, original_name, file_path, file_size, mime_type, media_type, file_hash, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                metadata = {
                    "relative_path": relative_path,
                    "upload_method": "folder_batch",
                    "upload_time": datetime.now().isoformat()
                }
                
                cursor.execute(query, (
                    target_filename,
                    os.path.basename(file_path),
                    target_path,
                    file_info['size'],
                    mime_type or 'application/octet-stream',
                    media_type,
                    file_hash,
                    json.dumps(metadata, ensure_ascii=False)
                ))
                
                media_id = cursor.lastrowid
            
            # 关联到知识条目
            if knowledge_id and media_id:
                cursor.execute("""
                    INSERT IGNORE INTO knowledge_media 
                    (knowledge_entry_id, media_file_id, media_role, description)
                    VALUES (%s, %s, %s, %s)
                """, (knowledge_id, media_id, 'attachment', f'项目文件: {relative_path}'))
            
            cursor.close()
            conn.close()
            
            return True, media_id
            
        except Exception as e:
            return False, str(e)
    
    def upload_folder(self, folder_path, create_knowledge=True):
        """上传整个文件夹"""
        folder_path = Path(folder_path).resolve()
        
        if not folder_path.exists() or not folder_path.is_dir():
            print(f"❌ 文件夹不存在: {folder_path}")
            return False
        
        print(f"🚀 开始上传文件夹: {folder_path.name}")
        
        # 扫描文件
        files_to_upload = self.scan_folder(folder_path)
        
        if not files_to_upload:
            print("❌ 没有找到支持的文件")
            return False
        
        # 创建知识条目
        knowledge_id = None
        if create_knowledge:
            knowledge_id = self.create_knowledge_entry(
                folder_path.name, str(folder_path), len(files_to_upload)
            )
        
        # 批量上传文件
        success_count = 0
        error_count = 0
        
        print(f"📤 开始上传 {len(files_to_upload)} 个文件...")
        
        for i, file_info in enumerate(files_to_upload, 1):
            print(f"[{i}/{len(files_to_upload)}] {file_info['relative_path']}", end=" ... ")
            
            success, result = self.upload_file_direct(file_info, knowledge_id)
            
            if success:
                print("✅")
                success_count += 1
            else:
                print(f"❌ {result}")
                error_count += 1
        
        print(f"\n🎉 上传完成!")
        print(f"✅ 成功: {success_count} 个文件")
        if error_count > 0:
            print(f"❌ 失败: {error_count} 个文件")
        
        if knowledge_id:
            print(f"🔗 知识条目ID: {knowledge_id}")
            print(f"🌐 查看地址: {self.server_url}/knowledge/{knowledge_id}")
        
        return True


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='批量上传文件夹到知识管理系统')
    parser.add_argument('folder_path', help='要上传的文件夹路径')
    parser.add_argument('--server', default='http://localhost:8081', help='服务器地址')
    parser.add_argument('--no-knowledge', action='store_true', help='不创建知识条目，只上传文件')
    
    args = parser.parse_args()
    
    uploader = FolderUploader(server_url=args.server)
    
    success = uploader.upload_folder(
        args.folder_path, 
        create_knowledge=not args.no_knowledge
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

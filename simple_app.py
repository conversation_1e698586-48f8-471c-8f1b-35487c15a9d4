#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版技术知识管理Web应用
用于测试和演示
"""

try:
    from flask import Flask, render_template_string, request, jsonify, send_file
    import mysql.connector
    import json
    import markdown
    from datetime import datetime
    import os
    print("✅ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'demo-secret-key'

# 简单的HTML模板
INDEX_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术知识管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        .knowledge-card { transition: transform 0.2s; margin-bottom: 1rem; }
        .knowledge-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .tag { background-color: #e9ecef; color: #495057; padding: 0.25rem 0.5rem; 
               border-radius: 0.25rem; font-size: 0.875rem; margin-right: 0.25rem; 
               margin-bottom: 0.25rem; display: inline-block; }
        .mermaid-diagram { text-align: center; margin: 1rem 0; padding: 1rem; 
                          border: 1px solid #dee2e6; border-radius: 0.375rem; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🧠 技术知识库</a>
            <form class="d-flex" action="/search" method="GET">
                <input class="form-control me-2" type="search" name="q" placeholder="搜索知识...">
                <button class="btn btn-outline-light" type="submit">搜索</button>
            </form>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h1 class="mb-4">最新知识条目</h1>
                
                {% for entry in entries %}
                <div class="card knowledge-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">
                                <a href="/knowledge/{{ entry.id }}" class="text-decoration-none">
                                    {{ entry.title }}
                                </a>
                            </h5>
                            <span class="badge bg-warning">{{ entry.difficulty_level }}</span>
                        </div>
                        
                        {% if entry.summary %}
                        <p class="card-text text-muted">{{ entry.summary[:150] }}...</p>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                {% if entry.category_name %}
                                <span class="badge bg-primary me-2">{{ entry.category_name }}</span>
                                {% endif %}
                                
                                {% if entry.tags %}
                                    {% for tag in entry.tags %}
                                    <span class="tag">{{ tag }}</span>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            
                            <small class="text-muted">
                                {{ entry.updated_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">系统状态</h5>
                    </div>
                    <div class="card-body">
                        <p>✅ 数据库连接正常</p>
                        <p>📚 共有 {{ entries|length }} 个知识条目</p>
                        <p>🔍 支持全文搜索</p>
                        <p>📊 支持图表展示</p>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/search" class="btn btn-outline-primary">高级搜索</a>
                            <a href="/api/test" class="btn btn-outline-success">API测试</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
</body>
</html>
'''

DETAIL_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ entry.title }} - 技术知识库</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block { background-color: #f8f9fa; padding: 1rem; border-radius: 0.375rem; margin: 1rem 0; }
        .mermaid-diagram { text-align: center; margin: 1rem 0; padding: 1rem; border: 1px solid #dee2e6; border-radius: 0.375rem; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🧠 技术知识库</a>
            <a href="/" class="btn btn-outline-light">返回首页</a>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h1>{{ entry.title }}</h1>
                {% if entry.summary %}
                <p class="text-muted">{{ entry.summary }}</p>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="knowledge-content">
                    {{ entry.content_html | safe }}
                </div>
            </div>
        </div>

        {% if entry.code_examples and entry.code_examples|length > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3>💻 代码示例</h3>
            </div>
            <div class="card-body">
                {% for code in entry.code_examples %}
                <div class="mb-4">
                    <h5>{{ code.title }} <span class="badge bg-secondary">{{ code.language }}</span></h5>
                    {% if code.description %}
                    <p class="text-muted">{{ code.description }}</p>
                    {% endif %}
                    <div class="code-block">
                        <pre><code>{{ code.code_content }}</code></pre>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if entry.diagrams and entry.diagrams|length > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3>📊 图表演示</h3>
            </div>
            <div class="card-body">
                {% for diagram in entry.diagrams %}
                <div class="mb-4">
                    <h5>{{ diagram.title }}</h5>
                    {% if diagram.description %}
                    <p class="text-muted">{{ diagram.description }}</p>
                    {% endif %}
                    <div class="mermaid-diagram">
                        <div class="mermaid">{{ diagram.diagram_definition }}</div>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if entry.media_files and entry.media_files|length > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3>🎵 多媒体内容</h3>
            </div>
            <div class="card-body">
                {% for media in entry.media_files %}
                <div class="mb-4">
                    <h5>{{ media.original_name }}
                        <span class="badge bg-info">{{ media.media_type }}</span>
                    </h5>
                    {% if media.role_description %}
                    <p class="text-muted">{{ media.role_description }}</p>
                    {% endif %}

                    {% if media.media_type == 'audio' %}
                    <div class="audio-player mb-3">
                        <audio controls class="w-100" preload="metadata">
                            <source src="/media/{{ media.id }}" type="{{ media.mime_type }}">
                            {% if media.mime_type == 'audio/aiff' %}
                            <source src="/media/{{ media.id }}" type="audio/x-aiff">
                            {% endif %}
                            您的浏览器不支持音频播放。请尝试使用Chrome、Safari或Firefox浏览器。
                        </audio>
                        <div class="audio-info mt-2">
                            <small class="text-muted">
                                🎵 {{ media.original_name }}
                                {% if media.duration_seconds %}
                                (时长: {{ "%.1f"|format(media.duration_seconds) }}秒)
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    {% if media.transcription %}
                    <div class="transcription">
                        <h6>📝 语音转录:</h6>
                        <div class="bg-light p-3 rounded">{{ media.transcription.transcription_text }}</div>
                    </div>
                    {% endif %}

                    {% elif media.media_type == 'video' %}
                    <div class="video-player mb-3">
                        <video controls class="w-100" style="max-height: 400px;">
                            <source src="/media/{{ media.id }}" type="{{ media.mime_type }}">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>

                    {% elif media.media_type == 'image' %}
                    <div class="image-display mb-3">
                        <img src="/media/{{ media.id }}" class="img-fluid rounded" alt="{{ media.original_name }}">
                    </div>
                    {% endif %}

                    {% if media.metadata %}
                    <div class="media-info">
                        <small class="text-muted">
                            文件大小: {{ "%.1f"|format(media.file_size / 1024 / 1024) }} MB
                            {% if media.duration_seconds %}
                            | 时长: {{ "%.1f"|format(media.duration_seconds) }} 秒
                            {% endif %}
                        </small>
                    </div>
                    {% endif %}
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <div class="card mt-4">
            <div class="card-body text-center">
                <a href="/" class="btn btn-primary">🏠 返回首页</a>
                <a href="/search" class="btn btn-outline-secondary">🔍 继续搜索</a>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html>
'''

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

@app.route('/')
def index():
    """首页"""
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 获取最新的知识条目
        query = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        ORDER BY ke.updated_at DESC
        LIMIT 10
        """
        cursor.execute(query)
        entries = cursor.fetchall()
        
        # 处理标签
        for entry in entries:
            if entry['tags']:
                try:
                    entry['tags'] = json.loads(entry['tags'])
                except:
                    entry['tags'] = []
            else:
                entry['tags'] = []
        
        cursor.close()
        conn.close()
        
        return render_template_string(INDEX_TEMPLATE, entries=entries)
    
    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/knowledge/<int:knowledge_id>')
def knowledge_detail(knowledge_id):
    """知识详情"""
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 获取知识条目
        query = """
        SELECT ke.*, kc.name as category_name 
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE ke.id = %s
        """
        cursor.execute(query, (knowledge_id,))
        entry = cursor.fetchone()
        
        if not entry:
            return "知识条目不存在", 404
        
        # 处理Markdown内容
        try:
            entry['content_html'] = markdown.markdown(entry['content'], extensions=['fenced_code'])
        except:
            entry['content_html'] = entry['content'].replace('\n', '<br>')
        
        # 获取代码示例
        query = "SELECT * FROM code_examples WHERE knowledge_entry_id = %s ORDER BY id"
        cursor.execute(query, (knowledge_id,))
        entry['code_examples'] = cursor.fetchall()
        
        # 获取图表
        query = "SELECT * FROM diagrams WHERE knowledge_entry_id = %s ORDER BY id"
        cursor.execute(query, (knowledge_id,))
        entry['diagrams'] = cursor.fetchall()

        # 获取媒体文件
        query = """
        SELECT mf.*, km.media_role, km.description as role_description
        FROM media_files mf
        JOIN knowledge_media km ON mf.id = km.media_file_id
        WHERE km.knowledge_entry_id = %s
        ORDER BY km.display_order, km.id
        """
        cursor.execute(query, (knowledge_id,))
        entry['media_files'] = cursor.fetchall()

        # 获取音频转录
        for media in entry['media_files']:
            if media['media_type'] == 'audio':
                query = "SELECT * FROM speech_transcriptions WHERE media_file_id = %s"
                cursor.execute(query, (media['id'],))
                media['transcription'] = cursor.fetchone()

        cursor.close()
        conn.close()

        return render_template_string(DETAIL_TEMPLATE, entry=entry)
    
    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/search')
def search():
    """搜索页面"""
    query = request.args.get('q', '')
    if not query:
        return render_template_string('<h1>搜索</h1><p>请输入搜索关键词</p>')
    
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 简单的LIKE搜索
        search_query = f"%{query}%"
        sql = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE ke.title LIKE %s OR ke.summary LIKE %s OR ke.content LIKE %s
        ORDER BY ke.updated_at DESC
        LIMIT 20
        """
        
        cursor.execute(sql, (search_query, search_query, search_query))
        results = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        html = f'<h1>搜索结果</h1><p>找到 {len(results)} 个结果</p>'
        for result in results:
            html += f'<div class="card mb-3"><div class="card-body">'
            html += f'<h5><a href="/knowledge/{result["id"]}">{result["title"]}</a></h5>'
            if result['summary']:
                html += f'<p>{result["summary"][:100]}...</p>'
            html += f'</div></div>'
        
        return html
    
    except Exception as e:
        return f"搜索错误: {str(e)}", 500

@app.route('/media/<int:media_id>')
def serve_media(media_id):
    """提供媒体文件服务"""
    try:
        print(f"🎵 请求媒体文件 ID: {media_id}")

        conn = get_db_connection()
        if not conn:
            print("❌ 数据库连接失败")
            return "数据库连接失败", 500

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT * FROM media_files WHERE id = %s", (media_id,))
        media = cursor.fetchone()

        cursor.close()
        conn.close()

        if not media:
            print(f"❌ 未找到媒体记录 ID: {media_id}")
            return "媒体记录不存在", 404

        print(f"📁 媒体文件路径: {media['file_path']}")
        print(f"📄 MIME类型: {media['mime_type']}")

        if not os.path.exists(media['file_path']):
            print(f"❌ 文件不存在: {media['file_path']}")
            return f"文件不存在: {media['file_path']}", 404

        print(f"✅ 提供媒体文件: {media['file_path']}")
        return send_file(media['file_path'], mimetype=media['mime_type'])

    except Exception as e:
        print(f"❌ 媒体服务错误: {e}")
        return f"错误: {str(e)}", 500

@app.route('/api/test')
def api_test():
    """API测试"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'})

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT COUNT(*) as count FROM knowledge_entries")
        result = cursor.fetchone()

        # 检查多媒体表是否存在
        multimedia_support = False
        try:
            cursor.execute("SELECT COUNT(*) as count FROM media_files")
            media_result = cursor.fetchone()
            multimedia_support = True
        except:
            media_result = {'count': 0}

        cursor.close()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': '系统运行正常',
            'knowledge_count': result['count'],
            'media_count': media_result['count'],
            'multimedia_support': multimedia_support,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    print("🚀 启动技术知识管理系统...")
    print("📊 检查数据库连接...")
    
    conn = get_db_connection()
    if conn:
        print("✅ 数据库连接成功")
        conn.close()
    else:
        print("❌ 数据库连接失败")
        exit(1)
    
    print("🌐 启动Web服务器...")
    print("📖 访问 http://localhost:8081 查看知识库")
    
    app.run(debug=True, host='0.0.0.0', port=8081)

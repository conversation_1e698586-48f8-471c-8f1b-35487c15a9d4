#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版技术知识管理Web应用
用于测试和演示
"""

try:
    from flask import Flask, render_template_string, request, jsonify, send_file, redirect, url_for
    import mysql.connector
    import json
    import markdown
    from datetime import datetime
    import os
    import hashlib
    import mimetypes
    from werkzeug.utils import secure_filename
    print("✅ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'demo-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB 最大文件大小

# 允许的文件类型
ALLOWED_EXTENSIONS = {
    'audio': {'mp3', 'wav', 'aiff', 'm4a', 'ogg'},
    'video': {'mp4', 'avi', 'mov', 'mkv', 'webm'},
    'image': {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'},
    'document': {'pdf', 'doc', 'docx', 'txt', 'md'}
}

# 简单的HTML模板
INDEX_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术知识管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        .knowledge-card { transition: transform 0.2s; margin-bottom: 1rem; }
        .knowledge-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .tag { background-color: #e9ecef; color: #495057; padding: 0.25rem 0.5rem; 
               border-radius: 0.25rem; font-size: 0.875rem; margin-right: 0.25rem; 
               margin-bottom: 0.25rem; display: inline-block; }
        .mermaid-diagram { text-align: center; margin: 1rem 0; padding: 1rem; 
                          border: 1px solid #dee2e6; border-radius: 0.375rem; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🧠 技术知识库</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link" href="/files">文件管理</a>
                <a class="nav-link" href="/upload">上传文件</a>
            </div>
            <form class="d-flex" action="/search" method="GET">
                <input class="form-control me-2" type="search" name="q" placeholder="搜索知识...">
                <button class="btn btn-outline-light" type="submit">搜索</button>
            </form>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h1 class="mb-4">最新知识条目</h1>
                
                {% for entry in entries %}
                <div class="card knowledge-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">
                                <a href="/knowledge/{{ entry.id }}" class="text-decoration-none">
                                    {{ entry.title }}
                                </a>
                            </h5>
                            <span class="badge bg-warning">{{ entry.difficulty_level }}</span>
                        </div>
                        
                        {% if entry.summary %}
                        <p class="card-text text-muted">{{ entry.summary[:150] }}...</p>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                {% if entry.category_name %}
                                <span class="badge bg-primary me-2">{{ entry.category_name }}</span>
                                {% endif %}
                                
                                {% if entry.tags %}
                                    {% for tag in entry.tags %}
                                    <span class="tag">{{ tag }}</span>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            
                            <small class="text-muted">
                                {{ entry.updated_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">系统状态</h5>
                    </div>
                    <div class="card-body">
                        <p>✅ 数据库连接正常</p>
                        <p>📚 共有 {{ entries|length }} 个知识条目</p>
                        <p>🔍 支持全文搜索</p>
                        <p>📊 支持图表展示</p>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/search" class="btn btn-outline-primary">🔍 高级搜索</a>
                            <a href="/files" class="btn btn-outline-info">📂 文件管理</a>
                            <a href="/upload" class="btn btn-outline-warning">📁 上传文件</a>
                            <a href="/api/test" class="btn btn-outline-success">🔧 API测试</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
</body>
</html>
'''

DETAIL_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ entry.title }} - 技术知识库</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block { background-color: #f8f9fa; padding: 1rem; border-radius: 0.375rem; margin: 1rem 0; }
        .mermaid-diagram { text-align: center; margin: 1rem 0; padding: 1rem; border: 1px solid #dee2e6; border-radius: 0.375rem; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🧠 技术知识库</a>
            <a href="/" class="btn btn-outline-light">返回首页</a>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h1>{{ entry.title }}</h1>
                {% if entry.summary %}
                <p class="text-muted">{{ entry.summary }}</p>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="knowledge-content">
                    {{ entry.content_html | safe }}
                </div>
            </div>
        </div>

        {% if entry.code_examples and entry.code_examples|length > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3>💻 代码示例</h3>
            </div>
            <div class="card-body">
                {% for code in entry.code_examples %}
                <div class="mb-4">
                    <h5>{{ code.title }} <span class="badge bg-secondary">{{ code.language }}</span></h5>
                    {% if code.description %}
                    <p class="text-muted">{{ code.description }}</p>
                    {% endif %}
                    <div class="code-block">
                        <pre><code>{{ code.code_content }}</code></pre>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if entry.diagrams and entry.diagrams|length > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3>📊 图表演示</h3>
            </div>
            <div class="card-body">
                {% for diagram in entry.diagrams %}
                <div class="mb-4">
                    <h5>{{ diagram.title }}</h5>
                    {% if diagram.description %}
                    <p class="text-muted">{{ diagram.description }}</p>
                    {% endif %}
                    <div class="mermaid-diagram">
                        <div class="mermaid">{{ diagram.diagram_definition }}</div>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if entry.media_files and entry.media_files|length > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3>🎵 多媒体内容</h3>
            </div>
            <div class="card-body">
                {% for media in entry.media_files %}
                <div class="mb-4">
                    <h5>{{ media.original_name }}
                        <span class="badge bg-info">{{ media.media_type }}</span>
                    </h5>
                    {% if media.role_description %}
                    <p class="text-muted">{{ media.role_description }}</p>
                    {% endif %}

                    {% if media.media_type == 'audio' %}
                    <div class="audio-player mb-3">
                        <audio controls class="w-100" preload="metadata">
                            <source src="/media/{{ media.id }}" type="{{ media.mime_type }}">
                            {% if media.mime_type == 'audio/aiff' %}
                            <source src="/media/{{ media.id }}" type="audio/x-aiff">
                            {% endif %}
                            您的浏览器不支持音频播放。请尝试使用Chrome、Safari或Firefox浏览器。
                        </audio>
                        <div class="audio-info mt-2">
                            <small class="text-muted">
                                🎵 {{ media.original_name }}
                                {% if media.duration_seconds %}
                                (时长: {{ "%.1f"|format(media.duration_seconds) }}秒)
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    {% if media.transcription %}
                    <div class="transcription">
                        <h6>📝 语音转录:</h6>
                        <div class="bg-light p-3 rounded">{{ media.transcription.transcription_text }}</div>
                    </div>
                    {% endif %}

                    {% elif media.media_type == 'video' %}
                    <div class="video-player mb-3">
                        <video controls class="w-100" style="max-height: 400px;">
                            <source src="/media/{{ media.id }}" type="{{ media.mime_type }}">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>

                    {% elif media.media_type == 'image' %}
                    <div class="image-display mb-3">
                        <img src="/media/{{ media.id }}" class="img-fluid rounded" alt="{{ media.original_name }}">
                    </div>
                    {% endif %}

                    {% if media.metadata %}
                    <div class="media-info">
                        <small class="text-muted">
                            文件大小: {{ "%.1f"|format(media.file_size / 1024 / 1024) }} MB
                            {% if media.duration_seconds %}
                            | 时长: {{ "%.1f"|format(media.duration_seconds) }} 秒
                            {% endif %}
                        </small>
                    </div>
                    {% endif %}
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <div class="card mt-4">
            <div class="card-body text-center">
                <a href="/" class="btn btn-primary">🏠 返回首页</a>
                <a href="/search" class="btn btn-outline-secondary">🔍 继续搜索</a>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html>
'''

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def allowed_file(filename, file_type=None):
    """检查文件类型是否允许 - 🚀 用户要求100%上传成功，移除所有限制！"""
    if not filename:
        return False

    # 🎯 用户明确要求：不准产生任何上传限制，必须100%全部上传！
    # 移除所有文件类型检查，允许上传任何文件

    # 只排除一些明显的系统垃圾文件
    excluded_patterns = [
        '.ds_store',     # macOS系统文件
        'thumbs.db',     # Windows缩略图
        'desktop.ini',   # Windows桌面配置
    ]

    filename_lower = filename.lower()

    # 检查是否是系统垃圾文件
    for pattern in excluded_patterns:
        if filename_lower == pattern:
            return False

    # 🎯 其他所有文件都无条件允许上传！
    return True

def get_media_type(filename):
    """根据文件扩展名确定媒体类型 - 支持所有文件类型，映射到数据库枚举值"""
    if not filename:
        return 'document'

    # 无扩展名文件也允许，归类为文档
    if '.' not in filename:
        return 'document'

    extension = filename.rsplit('.', 1)[1].lower()

    # 🎯 映射到数据库支持的枚举值: 'audio','video','image','document'
    # 音频文件
    if extension in {'mp3', 'wav', 'aiff', 'm4a', 'ogg', 'flac', 'aac', 'wma'}:
        return 'audio'

    # 视频文件
    if extension in {'mp4', 'avi', 'mov', 'mkv', 'webm', 'wmv', 'flv', 'm4v'}:
        return 'video'

    # 图片文件
    if extension in {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff'}:
        return 'image'

    # 🚀 所有其他文件类型（代码、配置、数据等）都归类为 'document'
    # 这样确保100%兼容数据库枚举，不会再有截断错误
    return 'document'

def calculate_file_hash(file_path):
    """计算文件MD5哈希"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def save_uploaded_file(file, knowledge_id=None):
    """保存上传的文件"""
    print(f"🔍 检查文件: {file.filename}")

    if not file:
        print("❌ 文件对象为空")
        return None, "文件对象为空"

    if not allowed_file(file.filename):
        print(f"❌ 不支持的文件类型: {file.filename}")
        return None, "不支持的文件类型"

    print("✅ 文件类型检查通过")

    try:
        # 安全的文件名
        filename = secure_filename(file.filename)
        media_type = get_media_type(filename)

        # 创建目录
        upload_dir = f"media/{media_type}"
        os.makedirs(upload_dir, exist_ok=True)

        # 临时保存文件以计算哈希
        temp_path = os.path.join(upload_dir, f"temp_{filename}")
        file.save(temp_path)

        # 计算哈希并重命名
        file_hash = calculate_file_hash(temp_path)
        file_ext = os.path.splitext(filename)[1] if '.' in filename else ''
        final_filename = f"{file_hash}{file_ext}" if file_ext else file_hash
        final_path = os.path.join(upload_dir, final_filename)

        # 检查是否已存在
        if os.path.exists(final_path):
            os.remove(temp_path)
        else:
            os.rename(temp_path, final_path)

        # 获取文件信息
        file_size = os.path.getsize(final_path)
        mime_type, _ = mimetypes.guess_type(final_path)

        # 保存到数据库
        conn = get_db_connection()
        if not conn:
            print("❌ 数据库连接失败")
            return None, "数据库连接失败"

        cursor = None
        media_id = None

        try:
            # 开始事务
            conn.start_transaction()
            cursor = conn.cursor(dictionary=True, buffered=True)  # 添加buffered=True

            print(f"🔍 检查文件哈希: {file_hash}")

            # 检查是否已存在相同哈希的文件
            cursor.execute("SELECT id FROM media_files WHERE file_hash = %s", (file_hash,))
            existing = cursor.fetchone()
            cursor.fetchall()  # 清空结果集

            if existing:
                media_id = existing['id']
                print(f"📁 文件已存在，使用现有ID: {media_id}")
            else:
                # 插入新记录
                query = """
                INSERT INTO media_files
                (filename, original_name, file_path, file_size, mime_type, media_type, file_hash, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """

                metadata = {
                    "upload_time": datetime.now().isoformat(),
                    "original_filename": filename
                }

                print(f"💾 插入新媒体记录...")
                cursor.execute(query, (
                    final_filename, filename, final_path, file_size,
                    mime_type or 'application/octet-stream', media_type,
                    file_hash, json.dumps(metadata, ensure_ascii=False)
                ))

                media_id = cursor.lastrowid
                print(f"✅ 新媒体记录ID: {media_id}")

            # 如果指定了知识条目，创建关联
            if knowledge_id and media_id:
                query = """
                INSERT IGNORE INTO knowledge_media
                (knowledge_entry_id, media_file_id, media_role, description)
                VALUES (%s, %s, %s, %s)
                """
                cursor.execute(query, (
                    knowledge_id, media_id, 'attachment', f'用户上传的{media_type}文件'
                ))
                print(f"🔗 创建关联: 知识条目{knowledge_id} -> 媒体{media_id}")

            # 提交事务
            conn.commit()
            print(f"✅ 数据库事务提交成功")

        except Exception as db_error:
            # 回滚事务
            if conn:
                conn.rollback()
            print(f"❌ 数据库操作失败: {db_error}")
            return None, f"数据库操作失败: {str(db_error)}"

        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

        return media_id, "文件上传成功"

    except Exception as e:
        return None, f"上传失败: {str(e)}"

@app.route('/')
def index():
    """首页"""
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 获取最新的知识条目
        query = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        ORDER BY ke.updated_at DESC
        LIMIT 10
        """
        cursor.execute(query)
        entries = cursor.fetchall()
        
        # 处理标签
        for entry in entries:
            if entry['tags']:
                try:
                    entry['tags'] = json.loads(entry['tags'])
                except:
                    entry['tags'] = []
            else:
                entry['tags'] = []
        
        cursor.close()
        conn.close()
        
        return render_template_string(INDEX_TEMPLATE, entries=entries)
    
    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/knowledge/<int:knowledge_id>')
def knowledge_detail(knowledge_id):
    """知识详情"""
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 获取知识条目
        query = """
        SELECT ke.*, kc.name as category_name 
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE ke.id = %s
        """
        cursor.execute(query, (knowledge_id,))
        entry = cursor.fetchone()
        
        if not entry:
            return "知识条目不存在", 404
        
        # 处理Markdown内容
        try:
            entry['content_html'] = markdown.markdown(entry['content'], extensions=['fenced_code'])
        except:
            entry['content_html'] = entry['content'].replace('\n', '<br>')
        
        # 获取代码示例
        query = "SELECT * FROM code_examples WHERE knowledge_entry_id = %s ORDER BY id"
        cursor.execute(query, (knowledge_id,))
        entry['code_examples'] = cursor.fetchall()
        
        # 获取图表
        query = "SELECT * FROM diagrams WHERE knowledge_entry_id = %s ORDER BY id"
        cursor.execute(query, (knowledge_id,))
        entry['diagrams'] = cursor.fetchall()

        # 获取媒体文件
        query = """
        SELECT mf.*, km.media_role, km.description as role_description
        FROM media_files mf
        JOIN knowledge_media km ON mf.id = km.media_file_id
        WHERE km.knowledge_entry_id = %s
        ORDER BY km.display_order, km.id
        """
        cursor.execute(query, (knowledge_id,))
        entry['media_files'] = cursor.fetchall()

        # 获取音频转录
        for media in entry['media_files']:
            if media['media_type'] == 'audio':
                query = "SELECT * FROM speech_transcriptions WHERE media_file_id = %s"
                cursor.execute(query, (media['id'],))
                media['transcription'] = cursor.fetchone()

        cursor.close()
        conn.close()

        return render_template_string(DETAIL_TEMPLATE, entry=entry)
    
    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/search')
def search():
    """搜索页面"""
    query = request.args.get('q', '')
    if not query:
        return render_template_string('<h1>搜索</h1><p>请输入搜索关键词</p>')
    
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 简单的LIKE搜索
        search_query = f"%{query}%"
        sql = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE ke.title LIKE %s OR ke.summary LIKE %s OR ke.content LIKE %s
        ORDER BY ke.updated_at DESC
        LIMIT 20
        """
        
        cursor.execute(sql, (search_query, search_query, search_query))
        results = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        html = f'<h1>搜索结果</h1><p>找到 {len(results)} 个结果</p>'
        for result in results:
            html += f'<div class="card mb-3"><div class="card-body">'
            html += f'<h5><a href="/knowledge/{result["id"]}">{result["title"]}</a></h5>'
            if result['summary']:
                html += f'<p>{result["summary"][:100]}...</p>'
            html += f'</div></div>'
        
        return html
    
    except Exception as e:
        return f"搜索错误: {str(e)}", 500

@app.route('/media/<int:media_id>')
def serve_media(media_id):
    """提供媒体文件服务"""
    try:
        print(f"🎵 请求媒体文件 ID: {media_id}")

        conn = get_db_connection()
        if not conn:
            print("❌ 数据库连接失败")
            return "数据库连接失败", 500

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT * FROM media_files WHERE id = %s", (media_id,))
        media = cursor.fetchone()

        cursor.close()
        conn.close()

        if not media:
            print(f"❌ 未找到媒体记录 ID: {media_id}")
            return "媒体记录不存在", 404

        print(f"📁 媒体文件路径: {media['file_path']}")
        print(f"📄 MIME类型: {media['mime_type']}")

        if not os.path.exists(media['file_path']):
            print(f"❌ 文件不存在: {media['file_path']}")
            return f"文件不存在: {media['file_path']}", 404

        print(f"✅ 提供媒体文件: {media['file_path']}")
        return send_file(media['file_path'], mimetype=media['mime_type'])

    except Exception as e:
        print(f"❌ 媒体服务错误: {e}")
        return f"错误: {str(e)}", 500

@app.route('/files')
def files_list():
    """文件列表页面"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = 20
        search_query = request.args.get('q', '').strip()
        media_type_filter = request.args.get('type', '')

        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500

        cursor = conn.cursor(dictionary=True)

        # 构建查询条件
        where_conditions = []
        params = []

        if search_query:
            where_conditions.append("(original_name LIKE %s OR filename LIKE %s)")
            params.extend([f'%{search_query}%', f'%{search_query}%'])

        if media_type_filter:
            where_conditions.append("media_type = %s")
            params.append(media_type_filter)

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM media_files {where_clause}"
        cursor.execute(count_query, params)
        total_files = cursor.fetchone()['total']

        # 获取文件列表
        offset = (page - 1) * per_page
        files_query = f"""
            SELECT id, filename, original_name, file_size, mime_type, media_type,
                   upload_time, file_hash
            FROM media_files
            {where_clause}
            ORDER BY upload_time DESC
            LIMIT %s OFFSET %s
        """
        cursor.execute(files_query, params + [per_page, offset])
        files = cursor.fetchall()

        # 获取媒体类型统计
        cursor.execute("""
            SELECT media_type, COUNT(*) as count
            FROM media_files
            GROUP BY media_type
            ORDER BY count DESC
        """)
        type_stats = cursor.fetchall()

        cursor.close()
        conn.close()

        # 计算分页信息
        total_pages = (total_files + per_page - 1) // per_page

        # 格式化文件大小
        def format_file_size(size_bytes):
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

        for file in files:
            file['formatted_size'] = format_file_size(file['file_size'])
            file['upload_time_formatted'] = file['upload_time'].strftime('%Y-%m-%d %H:%M:%S')

        files_template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理 - 技术知识库</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🧠 技术知识库</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link active" href="/files">文件管理</a>
                <a class="nav-link" href="/upload">上传文件</a>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="row">
            <div class="col-md-3">
                <!-- 侧边栏：筛选和统计 -->
                <div class="card">
                    <div class="card-header">
                        <h5>📊 文件统计</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>总文件数：</strong>{{ total_files }}</p>

                        <h6>按类型分布：</h6>
                        <div class="list-group list-group-flush">
                            <a href="/files" class="list-group-item list-group-item-action {% if not media_type_filter %}active{% endif %}">
                                📁 全部文件 ({{ total_files }})
                            </a>
                            {% for stat in type_stats %}
                            <a href="/files?type={{ stat.media_type }}"
                               class="list-group-item list-group-item-action {% if media_type_filter == stat.media_type %}active{% endif %}">
                                {% if stat.media_type == 'audio' %}🎵{% elif stat.media_type == 'video' %}🎬{% elif stat.media_type == 'image' %}🖼️{% else %}📄{% endif %}
                                {{ stat.media_type|title }} ({{ stat.count }})
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <!-- 搜索栏 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="q"
                                       value="{{ search_query }}" placeholder="搜索文件名...">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                {% if search_query or media_type_filter %}
                                <a href="/files" class="btn btn-outline-secondary">清除</a>
                                {% endif %}
                            </div>
                            {% if media_type_filter %}
                            <input type="hidden" name="type" value="{{ media_type_filter }}">
                            {% endif %}
                        </form>
                    </div>
                </div>

                <!-- 文件列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>📁 文件列表</h5>
                        <a href="/upload" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 上传文件
                        </a>
                    </div>
                    <div class="card-body">
                        {% if files %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>类型</th>
                                        <th>大小</th>
                                        <th>上传时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for file in files %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">
                                                    {% if file.media_type == 'audio' %}🎵
                                                    {% elif file.media_type == 'video' %}🎬
                                                    {% elif file.media_type == 'image' %}🖼️
                                                    {% else %}📄{% endif %}
                                                </span>
                                                <div>
                                                    <div class="fw-bold">{{ file.original_name }}</div>
                                                    <small class="text-muted">{{ file.filename }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ file.media_type }}</span>
                                            <br><small class="text-muted">{{ file.mime_type }}</small>
                                        </td>
                                        <td>{{ file.formatted_size }}</td>
                                        <td>{{ file.upload_time_formatted }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="/media/{{ file.id }}" class="btn btn-outline-primary"
                                                   title="预览/下载">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/download/{{ file.id }}" class="btn btn-outline-success"
                                                   title="下载" download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button class="btn btn-outline-info"
                                                        onclick="showFileInfo({{ file.id }})" title="详情">
                                                    <i class="fas fa-info"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        {% if total_pages > 1 %}
                        <nav aria-label="文件列表分页">
                            <ul class="pagination justify-content-center">
                                {% if page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page - 1 }}{% if search_query %}&q={{ search_query }}{% endif %}{% if media_type_filter %}&type={{ media_type_filter }}{% endif %}">上一页</a>
                                </li>
                                {% endif %}

                                {% for p in range(1, total_pages + 1) %}
                                    {% if p == page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ p }}</span>
                                    </li>
                                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ p }}{% if search_query %}&q={{ search_query }}{% endif %}{% if media_type_filter %}&type={{ media_type_filter }}{% endif %}">{{ p }}</a>
                                    </li>
                                    {% elif p == 4 or p == total_pages - 3 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page < total_pages %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page + 1 }}{% if search_query %}&q={{ search_query }}{% endif %}{% if media_type_filter %}&type={{ media_type_filter }}{% endif %}">下一页</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5>没有找到文件</h5>
                            <p class="text-muted">
                                {% if search_query %}
                                没有找到匹配"{{ search_query }}"的文件
                                {% else %}
                                还没有上传任何文件
                                {% endif %}
                            </p>
                            <a href="/upload" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 上传第一个文件
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 文件详情模态框 -->
    <div class="modal fade" id="fileInfoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">文件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="fileInfoContent">
                    加载中...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showFileInfo(fileId) {
            const modal = new bootstrap.Modal(document.getElementById('fileInfoModal'));
            const content = document.getElementById('fileInfoContent');

            content.innerHTML = '加载中...';
            modal.show();

            fetch(`/api/file/${fileId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const file = data.file;
                        content.innerHTML = `
                            <table class="table table-borderless">
                                <tr><th>文件名:</th><td>${file.original_name}</td></tr>
                                <tr><th>存储名:</th><td>${file.filename}</td></tr>
                                <tr><th>文件大小:</th><td>${file.formatted_size}</td></tr>
                                <tr><th>MIME类型:</th><td>${file.mime_type}</td></tr>
                                <tr><th>媒体类型:</th><td>${file.media_type}</td></tr>
                                <tr><th>文件哈希:</th><td><code>${file.file_hash}</code></td></tr>
                                <tr><th>上传时间:</th><td>${file.upload_time_formatted}</td></tr>
                                <tr><th>文件路径:</th><td><code>${file.file_path}</code></td></tr>
                            </table>
                            <div class="mt-3">
                                <a href="/media/${file.id}" class="btn btn-primary me-2">预览/打开</a>
                                <a href="/download/${file.id}" class="btn btn-success" download>下载文件</a>
                            </div>
                        `;
                    } else {
                        content.innerHTML = `<div class="alert alert-danger">加载失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    content.innerHTML = `<div class="alert alert-danger">加载错误: ${error}</div>`;
                });
        }
    </script>
</body>
</html>
        '''

        return render_template_string(files_template,
                                    files=files,
                                    total_files=total_files,
                                    type_stats=type_stats,
                                    search_query=search_query,
                                    media_type_filter=media_type_filter,
                                    page=page,
                                    total_pages=total_pages)

    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/download/<int:media_id>')
def download_file(media_id):
    """文件下载"""
    try:
        conn = get_db_connection()
        if not conn:
            return "数据库连接失败", 500

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT * FROM media_files WHERE id = %s", (media_id,))
        media = cursor.fetchone()

        cursor.close()
        conn.close()

        if not media:
            return "文件不存在", 404

        if not os.path.exists(media['file_path']):
            return "文件已被删除", 404

        return send_file(
            media['file_path'],
            as_attachment=True,
            download_name=media['original_name'],
            mimetype=media['mime_type']
        )

    except Exception as e:
        return f"下载错误: {str(e)}", 500

@app.route('/api/file/<int:media_id>')
def api_file_info(media_id):
    """获取文件详情API"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'error': '数据库连接失败'})

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT * FROM media_files WHERE id = %s", (media_id,))
        media = cursor.fetchone()

        cursor.close()
        conn.close()

        if not media:
            return jsonify({'success': False, 'error': '文件不存在'})

        # 格式化文件大小
        def format_file_size(size_bytes):
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

        media['formatted_size'] = format_file_size(media['file_size'])
        media['upload_time_formatted'] = media['upload_time'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({'success': True, 'file': media})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test')
def api_test():
    """API测试"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'})

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT COUNT(*) as count FROM knowledge_entries")
        result = cursor.fetchone()

        # 检查多媒体表是否存在
        multimedia_support = False
        try:
            cursor.execute("SELECT COUNT(*) as count FROM media_files")
            media_result = cursor.fetchone()
            multimedia_support = True
        except:
            media_result = {'count': 0}

        cursor.close()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': '系统运行正常',
            'knowledge_count': result['count'],
            'media_count': media_result['count'],
            'multimedia_support': multimedia_support,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """文件上传页面和处理"""
    if request.method == 'GET':
        # 显示上传页面
        upload_template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传 - 技术知识库</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🧠 技术知识库</a>
            <a href="/" class="btn btn-outline-light">返回首页</a>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>📁 上传多媒体文件</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="file" class="form-label">选择文件或拖拽文件夹</label>
                                <input type="file" class="form-control" id="file" name="file"
                                       accept="audio/*,video/*,image/*,.pdf,.doc,.docx,.txt,.md,.py,.js,.html,.css,.json,.xml,.yaml,.yml,.swift,.m,.h,.cpp,.c,.java,.kt,.go,.rs,.php,.rb,.sh,.sql,.log"
                                       multiple webkitdirectory>
                                <div class="form-text">
                                    支持的格式：
                                    <br>🎵 音频: MP3, WAV, AIFF, M4A, OGG
                                    <br>🎬 视频: MP4, AVI, MOV, MKV, WEBM
                                    <br>🖼️ 图片: JPG, PNG, GIF, BMP, WEBP
                                    <br>📄 文档: PDF, DOC, DOCX, TXT, MD
                                    <br>💻 代码: PY, JS, HTML, CSS, SWIFT, JAVA, GO等
                                    <br>📁 支持整个文件夹上传
                                    <br>最大单文件大小: 100MB
                                </div>

                                <!-- 拖拽上传区域 -->
                                <div id="drop-zone" class="mt-3 p-4 border-2 border-dashed border-primary rounded text-center"
                                     style="min-height: 150px; background-color: #f8f9ff;">
                                    <div class="drop-zone-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                        <h5>拖拽文件夹到这里</h5>
                                        <p class="text-muted">或者点击上方"选择文件"按钮选择文件夹</p>
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('file').click()">
                                            📁 选择文件夹
                                        </button>
                                    </div>
                                    <div id="upload-progress" style="display: none;">
                                        <div class="progress mb-2">
                                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <div id="upload-status">准备上传...</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="knowledge_id" class="form-label">关联知识条目 (可选)</label>
                                <select class="form-select" id="knowledge_id" name="knowledge_id">
                                    <option value="">不关联到特定知识条目</option>
                                    {% for entry in knowledge_entries %}
                                    <option value="{{ entry.id }}">{{ entry.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">文件描述 (可选)</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="描述这个文件的内容和用途..."></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> 上传文件
                            </button>
                        </form>
                    </div>
                </div>

                <script>
                    // 文件夹拖拽上传功能
                    const dropZone = document.getElementById('drop-zone');
                    const fileInput = document.getElementById('file');
                    const uploadProgress = document.getElementById('upload-progress');
                    const uploadStatus = document.getElementById('upload-status');
                    const progressBar = document.querySelector('.progress-bar');

                    // 防止默认拖拽行为
                    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                        dropZone.addEventListener(eventName, preventDefaults, false);
                        document.body.addEventListener(eventName, preventDefaults, false);
                    });

                    function preventDefaults(e) {
                        e.preventDefault();
                        e.stopPropagation();
                    }

                    // 拖拽视觉反馈
                    ['dragenter', 'dragover'].forEach(eventName => {
                        dropZone.addEventListener(eventName, highlight, false);
                    });

                    ['dragleave', 'drop'].forEach(eventName => {
                        dropZone.addEventListener(eventName, unhighlight, false);
                    });

                    function highlight(e) {
                        dropZone.classList.add('border-success');
                        dropZone.style.backgroundColor = '#e8f5e8';
                    }

                    function unhighlight(e) {
                        dropZone.classList.remove('border-success');
                        dropZone.style.backgroundColor = '#f8f9ff';
                    }

                    // 处理文件拖拽
                    dropZone.addEventListener('drop', handleDrop, false);

                    function handleDrop(e) {
                        const dt = e.dataTransfer;
                        const files = dt.files;

                        if (files.length > 0) {
                            handleFiles(files);
                        }
                    }

                    // 处理文件选择
                    fileInput.addEventListener('change', function(e) {
                        handleFiles(e.target.files);
                    });

                    // 批量上传文件
                    async function handleFiles(files) {
                        if (files.length === 0) return;

                        // 显示进度条
                        document.querySelector('.drop-zone-content').style.display = 'none';
                        uploadProgress.style.display = 'block';

                        const totalFiles = files.length;
                        let uploadedFiles = 0;
                        let successCount = 0;
                        let errorCount = 0;

                        uploadStatus.textContent = `准备上传 ${totalFiles} 个文件...`;

                        // 获取知识条目ID
                        const knowledgeId = document.getElementById('knowledge_id').value;

                        for (let i = 0; i < files.length; i++) {
                            const file = files[i];

                            try {
                                uploadStatus.textContent = `正在上传: ${file.name} (${i + 1}/${totalFiles})`;

                                const result = await uploadSingleFile(file, knowledgeId);

                                if (result.success) {
                                    successCount++;
                                } else {
                                    errorCount++;
                                    console.error(`上传失败: ${file.name}`, result.error);
                                }

                            } catch (error) {
                                errorCount++;
                                console.error(`上传错误: ${file.name}`, error);
                            }

                            uploadedFiles++;
                            const progress = (uploadedFiles / totalFiles) * 100;
                            progressBar.style.width = progress + '%';
                            progressBar.textContent = Math.round(progress) + '%';
                        }

                        // 上传完成
                        uploadStatus.innerHTML = `
                            上传完成！<br>
                            ✅ 成功: ${successCount} 个文件<br>
                            ${errorCount > 0 ? `❌ 失败: ${errorCount} 个文件` : ''}
                        `;

                        // 3秒后重置界面
                        setTimeout(() => {
                            document.querySelector('.drop-zone-content').style.display = 'block';
                            uploadProgress.style.display = 'none';
                            progressBar.style.width = '0%';
                            fileInput.value = '';

                            // 如果有关联知识条目且全部成功，跳转到详情页
                            if (knowledgeId && errorCount === 0) {
                                window.location.href = `/knowledge/${knowledgeId}`;
                            }
                        }, 3000);
                    }

                    // 上传单个文件
                    async function uploadSingleFile(file, knowledgeId) {
                        const formData = new FormData();
                        formData.append('file', file);
                        if (knowledgeId) {
                            formData.append('knowledge_id', knowledgeId);
                        }
                        formData.append('description', `来自文件夹: ${file.webkitRelativePath || file.name}`);

                        const response = await fetch('/upload', {
                            method: 'POST',
                            body: formData
                        });

                        if (response.ok) {
                            const result = await response.json();
                            return { success: true, data: result };
                        } else {
                            const error = await response.text();
                            return { success: false, error: error };
                        }
                    }

                    // 切换上传模式
                    function toggleUploadMode() {
                        const fileInput = document.getElementById('file');
                        const isFolderMode = fileInput.hasAttribute('webkitdirectory');

                        if (isFolderMode) {
                            // 切换到文件模式
                            fileInput.removeAttribute('webkitdirectory');
                            fileInput.removeAttribute('multiple');
                            document.querySelector('.drop-zone-content h5').textContent = '拖拽文件到这里';
                            document.querySelector('.drop-zone-content p').textContent = '或者点击上方"选择文件"按钮选择文件';
                        } else {
                            // 切换到文件夹模式
                            fileInput.setAttribute('webkitdirectory', '');
                            fileInput.setAttribute('multiple', '');
                            document.querySelector('.drop-zone-content h5').textContent = '拖拽文件夹到这里';
                            document.querySelector('.drop-zone-content p').textContent = '或者点击上方"选择文件"按钮选择文件夹';
                        }
                    }
                </script>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5>💡 使用提示</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>视频文件</strong>：上传后可以在知识详情页面直接播放</li>
                            <li><strong>音频文件</strong>：系统会尝试自动转录为文字</li>
                            <li><strong>图片文件</strong>：系统会尝试提取其中的文字内容</li>
                            <li><strong>文档文件</strong>：可以作为知识的补充资料</li>
                            <li><strong>关联知识</strong>：选择相关的知识条目，文件会显示在该条目的详情页面</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
        '''

        # 获取所有知识条目用于关联选择
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT id, title FROM knowledge_entries ORDER BY updated_at DESC")
            knowledge_entries = cursor.fetchall()
            cursor.close()
            conn.close()
        except:
            knowledge_entries = []

        return render_template_string(upload_template, knowledge_entries=knowledge_entries)

    else:
        # 处理文件上传
        print("🔄 开始处理文件上传...")

        if 'file' not in request.files:
            print("❌ 请求中没有文件")
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['file']
        print(f"📁 上传文件: {file.filename}")

        if file.filename == '':
            print("❌ 文件名为空")
            return jsonify({'error': '没有选择文件'}), 400

        knowledge_id = request.form.get('knowledge_id')
        print(f"🔗 关联知识条目ID: {knowledge_id}")

        if knowledge_id:
            try:
                knowledge_id = int(knowledge_id)
            except ValueError:
                print("❌ 知识条目ID格式错误")
                knowledge_id = None
        else:
            knowledge_id = None

        print("💾 开始保存文件...")
        media_id, message = save_uploaded_file(file, knowledge_id)
        print(f"📊 保存结果: media_id={media_id}, message={message}")

        if media_id:
            print(f"✅ 文件上传成功，媒体ID: {media_id}")
            if knowledge_id:
                print(f"🔄 重定向到知识条目: {knowledge_id}")
                return redirect(url_for('knowledge_detail', knowledge_id=knowledge_id))
            else:
                return jsonify({
                    'success': True,
                    'message': message,
                    'media_id': media_id,
                    'media_url': f'/media/{media_id}'
                })
        else:
            print(f"❌ 文件上传失败: {message}")
            return jsonify({'error': message}), 400

if __name__ == '__main__':
    print("🚀 启动技术知识管理系统...")
    print("📊 检查数据库连接...")
    
    conn = get_db_connection()
    if conn:
        print("✅ 数据库连接成功")
        conn.close()
    else:
        print("❌ 数据库连接失败")
        exit(1)
    
    print("🌐 启动Web服务器...")
    print("📖 访问 http://localhost:8082 查看知识库")

    app.run(debug=True, host='0.0.0.0', port=8082)

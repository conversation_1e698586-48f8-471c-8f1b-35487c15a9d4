# 数据库安全指南

## 当前安全状态检查

```bash
# 检查MySQL是否监听外部连接
netstat -an | grep 3306

# 检查MySQL用户权限
mysql -u root -e "SELECT User, Host FROM mysql.user;"

# 检查防火墙状态
sudo ufw status
```

## 建议的安全配置

### 1. 设置root密码
```sql
ALTER USER 'root'@'localhost' IDENTIFIED BY 'your_strong_password';
FLUSH PRIVILEGES;
```

### 2. 创建专用应用用户
```sql
CREATE USER 'knowledge_app'@'localhost' IDENTIFIED BY 'app_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON tech_knowledge_base.* TO 'knowledge_app'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 限制网络访问
在 `/opt/homebrew/etc/my.cnf` 中添加：
```ini
[mysqld]
bind-address = 127.0.0.1
skip-networking = false
```

### 4. 数据备份
```bash
# 定期备份
mysqldump -u root tech_knowledge_base > backup_$(date +%Y%m%d).sql
```

## 风险等级评估

- 🟢 **低风险**: 仅本地访问，主机安全
- 🟡 **中风险**: 有网络服务但配置得当
- 🔴 **高风险**: 暴露在公网，权限过大

当前状态：🟢 **低风险**（本地MySQL，无远程访问）
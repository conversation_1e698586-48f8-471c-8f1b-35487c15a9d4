# 📁 文件夹批量上传指南

## 🎯 三种上传方式

### 方式1: Web界面拖拽上传 (推荐)
**最简单的方式，直接在浏览器中使用**

1. 访问 http://localhost:8081/upload
2. 将整个文件夹拖拽到上传区域
3. 选择关联的知识条目（可选）
4. 点击上传，自动批量处理

**特点**：
- ✅ 无需安装额外软件
- ✅ 支持拖拽操作
- ✅ 实时进度显示
- ✅ 自动文件类型识别

### 方式2: 命令行工具
**适合开发者和自动化场景**

```bash
# 基本用法
python desktop_folder_uploader.py /path/to/your/project

# 高级用法
python desktop_folder_uploader.py /path/to/your/project --server http://localhost:8081

# 只上传文件，不创建知识条目
python desktop_folder_uploader.py /path/to/your/project --no-knowledge
```

**特点**：
- ✅ 命令行操作
- ✅ 支持脚本自动化
- ✅ 可配置服务器地址
- ✅ 详细的上传日志

### 方式3: GUI图形界面
**最用户友好的方式**

```bash
# 启动GUI工具
python gui_folder_uploader.py
```

**特点**：
- ✅ 图形界面操作
- ✅ 支持拖拽文件夹
- ✅ 实时进度条
- ✅ 详细日志显示

## 🔧 安装依赖

```bash
# 基本依赖
pip install requests mysql-connector-python

# GUI版本额外依赖（可选）
pip install tkinterdnd2
```

## 📊 支持的文件类型

### 代码文件
- **Python**: .py
- **JavaScript**: .js, .json
- **Web**: .html, .css, .xml
- **配置**: .yaml, .yml, .md, .txt
- **iOS/macOS**: .swift, .m, .h
- **其他**: .cpp, .c, .java, .kt, .go, .rs, .php, .rb, .sh, .sql

### 多媒体文件
- **音频**: .mp3, .wav, .aiff, .m4a, .ogg
- **视频**: .mp4, .avi, .mov, .mkv, .webm
- **图片**: .jpg, .jpeg, .png, .gif, .bmp, .webp

### 文档文件
- **Office**: .pdf, .doc, .docx, .ppt, .pptx, .xls, .xlsx
- **文本**: .txt, .md, .log

## 🚫 自动忽略的文件

系统会自动忽略以下文件和文件夹：

### 系统文件
- `.DS_Store` (macOS)
- `Thumbs.db` (Windows)
- `.gitignore`, `.git`

### 编译产物
- `__pycache__` (Python)
- `node_modules` (Node.js)
- `.vscode`, `.idea` (IDE配置)
- `build`, `dist`, `target`

### 临时文件
- `.tmp`, `.temp`, `.cache`
- 以 `.` 开头的隐藏文件（除了 `.gitignore`, `.env`）

## 🎯 使用场景示例

### 场景1: 上传iOS项目
```bash
python desktop_folder_uploader.py ~/Projects/MyiOSApp
```

**结果**：
- 自动识别 .swift, .m, .h 文件
- 忽略 build 文件夹和 .xcworkspace
- 创建"项目文件夹: MyiOSApp"知识条目
- 所有代码文件可在Web界面查看

### 场景2: 上传Web项目
```bash
python desktop_folder_uploader.py ~/Projects/MyWebsite
```

**结果**：
- 识别 .html, .css, .js, .json 文件
- 忽略 node_modules 文件夹
- 图片文件支持在线预览
- 代码文件支持语法高亮

### 场景3: 上传文档项目
```bash
python desktop_folder_uploader.py ~/Documents/TechSpecs
```

**结果**：
- 识别 .md, .pdf, .docx 文件
- 文本文件可直接在线查看
- PDF文件支持下载查看

## 🔄 工作流程

### 1. 扫描阶段
```
🔍 扫描文件夹: /Users/<USER>/Projects/MyApp
📊 找到 45 个支持的文件
```

### 2. 知识条目创建
```
✅ 创建知识条目成功，ID: 123
📝 标题: 项目文件夹: MyApp
📄 包含项目结构说明和文件统计
```

### 3. 文件上传
```
[1/45] src/main.swift ... ✅
[2/45] README.md ... ✅
[3/45] assets/icon.png ... ✅
...
[45/45] docs/api.md ... ✅
```

### 4. 完成
```
🎉 上传完成!
✅ 成功: 45 个文件
🔗 知识条目ID: 123
🌐 查看地址: http://localhost:8081/knowledge/123
```

## 🌐 Web界面功能

上传完成后，在Web界面可以：

### 查看项目结构
- 📁 按文件夹层级组织
- 🏷️ 文件类型标签
- 📊 文件大小信息

### 在线预览
- 📝 代码文件语法高亮
- 🖼️ 图片文件直接显示
- 🎵 音频文件在线播放
- 🎬 视频文件在线播放

### 搜索功能
- 🔍 按文件名搜索
- 📄 按文件内容搜索
- 🏷️ 按文件类型筛选

## 🛠️ 高级配置

### 自定义数据库连接
```python
# 修改 desktop_folder_uploader.py 中的数据库配置
db_config = {
    'host': 'your-host',
    'user': 'your-user', 
    'password': 'your-password',
    'database': 'your-database'
}
```

### 自定义文件类型支持
```python
# 在 FolderUploader 类中添加新的文件扩展名
self.supported_extensions['code'].add('.your_extension')
```

### 自定义忽略规则
```python
# 添加要忽略的文件或文件夹
self.ignore_patterns.add('your_ignore_pattern')
```

## 🚀 快速开始

1. **启动知识管理系统**
   ```bash
   python simple_app.py
   ```

2. **选择上传方式**
   - Web界面: 访问 http://localhost:8081/upload
   - 命令行: `python desktop_folder_uploader.py /path/to/folder`
   - GUI界面: `python gui_folder_uploader.py`

3. **上传您的项目文件夹**
   - 拖拽或选择文件夹
   - 等待上传完成
   - 在Web界面查看结果

## 💡 最佳实践

### 1. 项目组织
- 为每个项目创建单独的知识条目
- 使用有意义的文件夹名称
- 保持项目结构清晰

### 2. 文件管理
- 定期清理临时文件
- 使用 .gitignore 排除不需要的文件
- 保持文件命名规范

### 3. 知识管理
- 为项目添加详细的README
- 使用标签分类不同类型的项目
- 定期更新项目文档

现在您可以轻松地将桌面上的任何项目文件夹上传到知识管理系统中！🎉

# 技术知识管理系统

一个专门用于保存、搜索和展示AI助手提供的技术资料的完整解决方案。支持代码示例、图表演示、学习路径等多种形式的知识管理。

## 功能特点

### 📚 知识管理
- **多格式支持**：文本、代码、图表、链接
- **智能分类**：层级分类系统
- **标签系统**：灵活的标签管理
- **难度分级**：初级、中级、高级

### 🔍 强大搜索
- **全文搜索**：支持标题、摘要、内容搜索
- **标签搜索**：按标签精确查找
- **代码搜索**：在代码示例中搜索
- **分类筛选**：按技术分类过滤

### 📊 可视化展示
- **Mermaid图表**：流程图、时序图、类图等
- **代码高亮**：支持多种编程语言
- **响应式设计**：适配各种设备
- **学习路径**：结构化学习指导

## 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install flask mysql-connector-python markdown pygments

# 安装MySQL数据库
# macOS: brew install mysql
# Ubuntu: sudo apt-get install mysql-server
```

### 2. 数据库设置

```bash
# 启动MySQL服务
mysql -u root -p

# 创建数据库和表
mysql -u root -p < knowledge_database_schema.sql
```

### 3. 配置数据库连接

编辑 `knowledge_manager.py` 中的数据库连接参数：

```python
def __init__(self, host='localhost', user='root', password='your_password', database='tech_knowledge_base'):
```

### 4. 保存示例知识

```bash
# 保存Metal相关技术知识
python save_metal_knowledge.py
```

### 5. 启动Web应用

```bash
# 启动Flask应用
python knowledge_web_app.py

# 访问 http://localhost:5000
```

## 使用方法

### 保存新知识

```python
from knowledge_manager import KnowledgeManager

km = KnowledgeManager()

# 保存知识条目
knowledge_id = km.save_knowledge_entry(
    title="Metal坐标系详解",
    content="详细的技术内容...",
    summary="简短摘要",
    category_name="Metal编程",
    tags=["坐标系", "3D", "Metal"],
    difficulty="intermediate"
)

# 保存代码示例
km.save_code_example(
    knowledge_entry_id=knowledge_id,
    title="着色器代码",
    language="metal",
    code_content="vertex VertexOut main() { ... }",
    description="Metal着色器示例"
)

# 保存图表
km.save_diagram(
    knowledge_entry_id=knowledge_id,
    title="坐标变换流程",
    diagram_type="mermaid",
    diagram_definition="graph TD\n A --> B",
    description="展示坐标变换过程"
)

km.close()
```

### 搜索知识

```python
# 全文搜索
results = km.search_knowledge("Metal坐标系", search_type="fulltext")

# 标签搜索
results = km.search_knowledge("", search_type="tag", tags=["Metal", "3D"])

# 代码搜索
results = km.search_knowledge("vertex", search_type="code")

# 分类搜索
results = km.search_knowledge("", search_type="category", category="Metal编程")
```

### 创建学习路径

```python
# 创建学习路径
path_id = km.create_learning_path(
    name="Metal入门教程",
    description="从基础到进阶的Metal学习路径",
    knowledge_ids=[1, 2, 3, 4],  # 知识条目ID列表
    difficulty="beginner"
)
```

## API接口

### 保存知识 API

```bash
POST /api/save_knowledge
Content-Type: application/json

{
    "title": "知识标题",
    "content": "详细内容",
    "summary": "简短摘要",
    "category": "分类名称",
    "tags": ["标签1", "标签2"],
    "difficulty": "intermediate",
    "code_examples": [
        {
            "title": "代码示例标题",
            "language": "swift",
            "code_content": "代码内容",
            "description": "代码说明"
        }
    ],
    "diagrams": [
        {
            "title": "图表标题",
            "diagram_type": "mermaid",
            "diagram_definition": "graph TD\n A --> B",
            "description": "图表说明"
        }
    ]
}
```

### 搜索 API

```bash
GET /api/search?q=搜索词&type=fulltext&category=分类名
```

## 数据库结构

### 主要表结构

- `knowledge_entries` - 知识条目主表
- `knowledge_categories` - 知识分类表
- `code_examples` - 代码示例表
- `diagrams` - 图表演示表
- `learning_paths` - 学习路径表
- `learning_path_steps` - 学习路径步骤表

### 支持的图表类型

- **Mermaid图表**：flowchart, sequence, class, state, gantt
- **代码高亮**：支持100+编程语言
- **数学公式**：支持LaTeX数学公式

## 高级功能

### 1. 批量导入

```python
# 从JSON文件批量导入知识
def import_from_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for entry in data['entries']:
        km.save_knowledge_entry(**entry)
```

### 2. 知识导出

```python
# 导出知识为JSON格式
def export_knowledge(knowledge_id):
    entry = km.get_knowledge_with_examples(knowledge_id)
    return json.dumps(entry, ensure_ascii=False, indent=2)
```

### 3. 搜索历史分析

```python
# 分析搜索热词
km.cursor.execute("""
    SELECT search_query, COUNT(*) as count 
    FROM search_history 
    GROUP BY search_query 
    ORDER BY count DESC 
    LIMIT 10
""")
hot_searches = km.cursor.fetchall()
```

## 扩展建议

### 1. 集成AI助手
- 自动提取关键词作为标签
- 自动生成摘要
- 智能推荐相关知识

### 2. 协作功能
- 多用户支持
- 知识评论和评分
- 版本控制

### 3. 高级搜索
- 语义搜索
- 相似度推荐
- 知识图谱

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证用户名密码是否正确
   - 确认数据库是否存在

2. **中文显示乱码**
   - 确保数据库字符集为utf8mb4
   - 检查Python文件编码为UTF-8

3. **图表不显示**
   - 检查网络连接（需要加载CDN资源）
   - 验证Mermaid语法是否正确

### 性能优化

1. **数据库优化**
   - 为常用查询字段添加索引
   - 定期清理搜索历史
   - 使用连接池

2. **Web应用优化**
   - 启用缓存
   - 压缩静态资源
   - 使用CDN

## 许可证

MIT License - 可自由使用和修改

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**开始使用：**
1. 运行 `python save_metal_knowledge.py` 保存示例知识
2. 运行 `python knowledge_web_app.py` 启动Web应用
3. 访问 http://localhost:5000 开始探索！

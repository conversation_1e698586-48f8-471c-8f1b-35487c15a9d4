#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缺失文件 - 重新上传文件夹中的所有文件
确保数据库中包含完整的文件列表
"""

import os
import requests
import json
from pathlib import Path
import hashlib

def calculate_file_hash(file_path):
    """计算文件哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"❌ 计算哈希失败: {file_path} - {e}")
        return None

def upload_file_with_path(file_path, folder_path, base_url="http://localhost:8082"):
    """上传单个文件，包含文件夹路径"""
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f)}
            data = {'folder_path': folder_path}
            
            response = requests.post(f"{base_url}/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上传成功: {folder_path}/{os.path.basename(file_path)} - ID: {result.get('media_id', 'N/A')}")
                return True
            else:
                print(f"❌ 上传失败: {folder_path}/{os.path.basename(file_path)} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传错误: {folder_path}/{os.path.basename(file_path)} - {str(e)}")
        return False

def get_existing_files(base_url="http://localhost:8082"):
    """获取数据库中已存在的文件哈希值"""
    try:
        import mysql.connector
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        cursor.execute("SELECT file_hash, original_name FROM media_files")
        existing = {row[0]: row[1] for row in cursor.fetchall()}
        cursor.close()
        connection.close()
        return existing
    except Exception as e:
        print(f"❌ 获取现有文件失败: {e}")
        return {}

def fix_missing_files(root_folder, base_url="http://localhost:8082"):
    """修复缺失的文件"""
    root_path = Path(root_folder)
    if not root_path.exists() or not root_path.is_dir():
        print(f"❌ 文件夹不存在: {root_folder}")
        return
    
    print(f"🚀 开始修复缺失文件: {root_folder}")
    print(f"📡 服务器地址: {base_url}")
    
    # 获取现有文件
    existing_files = get_existing_files(base_url)
    print(f"📊 数据库中现有文件: {len(existing_files)} 个")
    
    success_count = 0
    skip_count = 0
    error_count = 0
    total_files = 0
    
    # 统计总文件数
    for root, dirs, files in os.walk(root_path):
        total_files += len(files)
    
    print(f"📊 文件夹中总文件数: {total_files}")
    
    # 处理所有文件
    for root, dirs, files in os.walk(root_path):
        current_path = Path(root)
        
        # 计算相对路径
        relative_path = current_path.relative_to(root_path)
        folder_path = str(relative_path) if str(relative_path) != '.' else ''
        
        print(f"\n📂 处理文件夹: {folder_path or '根目录'}")
        
        for file in files:
            file_path = current_path / file
            
            # 跳过隐藏文件和系统文件
            if file.startswith('.') or file in ['Thumbs.db', 'desktop.ini']:
                print(f"⏭️  跳过系统文件: {file}")
                continue
            
            # 计算文件哈希
            file_hash = calculate_file_hash(str(file_path))
            if not file_hash:
                error_count += 1
                continue
            
            # 检查文件是否已存在
            if file_hash in existing_files:
                print(f"⏭️  文件已存在: {file} (哈希: {file_hash[:8]}...)")
                skip_count += 1
                continue
            
            # 上传文件
            if upload_file_with_path(str(file_path), folder_path, base_url):
                success_count += 1
                existing_files[file_hash] = file  # 添加到已存在列表
            else:
                error_count += 1
            
            # 显示进度
            processed = success_count + skip_count + error_count
            progress = (processed / total_files) * 100
            print(f"📈 进度: {processed}/{total_files} ({progress:.1f}%)")
    
    print(f"\n🎉 修复完成!")
    print(f"✅ 新上传: {success_count} 个文件")
    print(f"⏭️  已存在: {skip_count} 个文件")
    print(f"❌ 失败: {error_count} 个文件")
    print(f"📊 总计: {success_count + skip_count + error_count} 个文件")

if __name__ == "__main__":
    folder_path = "/Users/<USER>/Downloads/Test2Dlib 2"
    print("🔧 修复缺失文件工具")
    print("=" * 50)
    
    fix_missing_files(folder_path)

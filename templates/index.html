{% extends "base.html" %}

{% block title %}首页 - 技术知识管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <h1 class="mb-4">最新知识条目</h1>
        
        {% if recent_entries %}
            {% for entry in recent_entries %}
            <div class="card knowledge-card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0">
                            <a href="{{ url_for('knowledge_detail', knowledge_id=entry.id) }}" 
                               class="text-decoration-none">
                                {{ entry.title }}
                            </a>
                        </h5>
                        <span class="badge bg-{{ 'success' if entry.difficulty_level == 'beginner' else 'warning' if entry.difficulty_level == 'intermediate' else 'danger' }} difficulty-badge">
                            {{ {'beginner': '初级', 'intermediate': '中级', 'advanced': '高级'}[entry.difficulty_level] }}
                        </span>
                    </div>
                    
                    {% if entry.summary %}
                    <p class="card-text text-muted">{{ entry.summary | truncate_words(100) }}</p>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if entry.category_name %}
                            <span class="badge bg-primary me-2">{{ entry.category_name }}</span>
                            {% endif %}
                            
                            {% for tag in entry.tags | format_tags %}
                            <span class="tag">{{ tag }}</span>
                            {% endfor %}
                        </div>
                        
                        <small class="text-muted">
                            更新于 {{ entry.updated_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="alert alert-info">
                <h4>欢迎使用技术知识管理系统！</h4>
                <p>这里还没有任何知识条目。您可以通过API添加新的技术资料。</p>
            </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">知识分类</h5>
            </div>
            <div class="card-body">
                {% if categories %}
                    {% for category in categories %}
                    <div class="mb-2">
                        <a href="{{ url_for('category_detail', category_id=category.id) }}" 
                           class="text-decoration-none">
                            <i class="fas fa-folder"></i> {{ category.name }}
                        </a>
                        {% if category.description %}
                        <small class="text-muted d-block">{{ category.description }}</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">暂无分类</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">快速操作</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('search') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> 高级搜索
                    </a>
                    <a href="{{ url_for('learning_paths') }}" class="btn btn-outline-success">
                        <i class="fas fa-route"></i> 学习路径
                    </a>
                    <a href="{{ url_for('categories') }}" class="btn btn-outline-info">
                        <i class="fas fa-tags"></i> 浏览分类
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">使用说明</h5>
            </div>
            <div class="card-body">
                <h6>如何保存知识？</h6>
                <p class="small">使用Python脚本调用API保存技术资料：</p>
                <pre class="small"><code>from knowledge_manager import KnowledgeManager
km = KnowledgeManager()
km.save_knowledge_entry(
    title="标题",
    content="内容",
    category_name="分类"
)</code></pre>
                
                <h6 class="mt-3">搜索技巧</h6>
                <ul class="small">
                    <li>全文搜索：直接输入关键词</li>
                    <li>标签搜索：使用逗号分隔多个标签</li>
                    <li>代码搜索：搜索代码片段</li>
                    <li>分类筛选：按技术分类过滤</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

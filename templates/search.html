{% extends "base.html" %}

{% block title %}搜索 - 技术知识管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">知识搜索</h1>
        
        <!-- 搜索表单 -->
        <div class="search-filters">
            <form method="GET" action="{{ url_for('search') }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="search-query" class="form-label">搜索关键词</label>
                            <input type="text" class="form-control" id="search-query" name="q" 
                                   value="{{ query }}" placeholder="输入搜索关键词...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="search-type" class="form-label">搜索类型</label>
                            <select class="form-select" id="search-type" name="type">
                                <option value="fulltext" {{ 'selected' if search_type == 'fulltext' }}>全文搜索</option>
                                <option value="tag" {{ 'selected' if search_type == 'tag' }}>标签搜索</option>
                                <option value="code" {{ 'selected' if search_type == 'code' }}>代码搜索</option>
                                <option value="category" {{ 'selected' if search_type == 'category' }}>分类搜索</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="category-filter" class="form-label">分类筛选</label>
                            <select class="form-select" id="category-filter" name="category">
                                <option value="">所有分类</option>
                                {% for category in categories %}
                                <option value="{{ category.name }}" {{ 'selected' if selected_category == category.name }}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('search') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> 清空
                        </a>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 搜索提示 -->
        {% if search_type == 'tag' %}
        <div class="alert alert-info">
            <strong>标签搜索提示：</strong> 使用逗号分隔多个标签，例如：Metal, 坐标系, 3D
        </div>
        {% endif %}
        
        <!-- 搜索结果 -->
        {% if query %}
        <div class="mt-4">
            <h3>搜索结果 
                <small class="text-muted">(找到 {{ results|length }} 个结果)</small>
            </h3>
            
            {% if results %}
                {% for result in results %}
                <div class="card knowledge-card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">
                                <a href="{{ url_for('knowledge_detail', knowledge_id=result.id) }}" 
                                   class="text-decoration-none">
                                    {{ result.title }}
                                </a>
                            </h5>
                            <span class="badge bg-{{ 'success' if result.difficulty_level == 'beginner' else 'warning' if result.difficulty_level == 'intermediate' else 'danger' }} difficulty-badge">
                                {{ {'beginner': '初级', 'intermediate': '中级', 'advanced': '高级'}[result.difficulty_level] }}
                            </span>
                        </div>
                        
                        {% if result.summary %}
                        <p class="card-text text-muted">{{ result.summary | truncate_words(150) }}</p>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                {% if result.category_name %}
                                <span class="badge bg-primary me-2">{{ result.category_name }}</span>
                                {% endif %}
                                
                                {% for tag in result.tags | format_tags %}
                                <span class="tag">{{ tag }}</span>
                                {% endfor %}
                                
                                {% if result.relevance %}
                                <small class="text-muted ms-2">相关度: {{ "%.2f"|format(result.relevance) }}</small>
                                {% endif %}
                            </div>
                            
                            <small class="text-muted">
                                更新于 {{ result.updated_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-warning">
                    <h4>没有找到相关结果</h4>
                    <p>请尝试：</p>
                    <ul>
                        <li>使用不同的关键词</li>
                        <li>检查拼写是否正确</li>
                        <li>尝试更通用的搜索词</li>
                        <li>使用标签搜索或分类筛选</li>
                    </ul>
                </div>
            {% endif %}
        </div>
        {% else %}
        <div class="mt-4">
            <div class="alert alert-info">
                <h4>搜索帮助</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>搜索类型说明：</h6>
                        <ul>
                            <li><strong>全文搜索</strong>：在标题、摘要、内容中搜索</li>
                            <li><strong>标签搜索</strong>：按技术标签精确查找</li>
                            <li><strong>代码搜索</strong>：在代码示例中搜索</li>
                            <li><strong>分类搜索</strong>：按技术分类浏览</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>搜索技巧：</h6>
                        <ul>
                            <li>使用具体的技术术语</li>
                            <li>标签搜索时用逗号分隔</li>
                            <li>结合分类筛选缩小范围</li>
                            <li>尝试不同的搜索类型</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

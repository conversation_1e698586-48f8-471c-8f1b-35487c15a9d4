-- 多媒体知识管理数据库扩展
-- 支持语音、视频、图片等高维度知识内容

USE tech_knowledge_base;

-- 1. 媒体文件表
CREATE TABLE media_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    media_type ENUM('audio', 'video', 'image', 'document') NOT NULL,
    duration_seconds INT DEFAULT NULL, -- 音频/视频时长
    file_hash VARCHAR(64) NOT NULL, -- 文件MD5哈希，防重复
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON, -- 存储额外的媒体信息
    INDEX idx_media_type (media_type),
    INDEX idx_file_hash (file_hash),
    INDEX idx_upload_time (upload_time)
);

-- 2. 语音转文字表
CREATE TABLE speech_transcriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    media_file_id INT NOT NULL,
    transcription_text LONGTEXT NOT NULL,
    confidence_score DECIMAL(3,2), -- 转录置信度 0.00-1.00
    language VARCHAR(10) DEFAULT 'zh-CN',
    transcription_engine VARCHAR(50), -- 使用的转录引擎
    timestamps JSON, -- 时间戳信息 [{"start": 0.5, "end": 2.3, "text": "hello"}]
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    FULLTEXT KEY ft_transcription (transcription_text),
    INDEX idx_media_file (media_file_id)
);

-- 3. 知识条目媒体关联表
CREATE TABLE knowledge_media (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    media_file_id INT NOT NULL,
    media_role ENUM('main_audio', 'explanation_audio', 'demo_video', 'illustration', 'attachment') NOT NULL,
    description TEXT,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    UNIQUE KEY unique_knowledge_media (knowledge_entry_id, media_file_id),
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_media_file (media_file_id),
    INDEX idx_media_role (media_role)
);

-- 4. 语音注释表（为特定时间点添加注释）
CREATE TABLE audio_annotations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    media_file_id INT NOT NULL,
    start_time DECIMAL(8,3) NOT NULL, -- 开始时间（秒）
    end_time DECIMAL(8,3) NOT NULL,   -- 结束时间（秒）
    annotation_text TEXT NOT NULL,
    annotation_type ENUM('note', 'highlight', 'question', 'summary') DEFAULT 'note',
    created_by VARCHAR(100), -- 创建者
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    INDEX idx_media_file (media_file_id),
    INDEX idx_time_range (start_time, end_time)
);

-- 5. 知识情感分析表
CREATE TABLE knowledge_sentiment (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT,
    media_file_id INT,
    sentiment_score DECIMAL(3,2), -- -1.00 到 1.00，负数表示消极，正数表示积极
    emotion_tags JSON, -- ["excited", "confident", "uncertain"] 等情感标签
    key_phrases JSON, -- 关键短语和重要性评分
    analysis_engine VARCHAR(50),
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_media_file (media_file_id)
);

-- 6. 多维度搜索索引表
CREATE TABLE knowledge_search_index (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    content_type ENUM('text', 'audio_transcript', 'image_ocr', 'video_caption') NOT NULL,
    searchable_content LONGTEXT NOT NULL,
    content_weight DECIMAL(3,2) DEFAULT 1.00, -- 搜索权重
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    FULLTEXT KEY ft_searchable_content (searchable_content),
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_content_type (content_type)
);

-- 7. 学习进度跟踪表
CREATE TABLE learning_progress (
    id INT PRIMARY KEY AUTO_INCREMENT,
    knowledge_entry_id INT NOT NULL,
    user_session VARCHAR(100) NOT NULL,
    media_file_id INT,
    progress_type ENUM('read', 'listened', 'watched', 'practiced') NOT NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00, -- 0.00-100.00
    time_spent_seconds INT DEFAULT 0,
    last_position DECIMAL(8,3), -- 音频/视频的最后播放位置
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_progress (knowledge_entry_id, user_session, media_file_id),
    INDEX idx_knowledge_entry (knowledge_entry_id),
    INDEX idx_user_session (user_session)
);

-- 8. 知识关联图表
CREATE TABLE knowledge_relationships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    source_knowledge_id INT NOT NULL,
    target_knowledge_id INT NOT NULL,
    relationship_type ENUM('prerequisite', 'related', 'extends', 'contradicts', 'example_of') NOT NULL,
    relationship_strength DECIMAL(3,2) DEFAULT 1.00, -- 0.00-1.00 关联强度
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_knowledge_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (target_knowledge_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_relationship (source_knowledge_id, target_knowledge_id, relationship_type),
    INDEX idx_source_knowledge (source_knowledge_id),
    INDEX idx_target_knowledge (target_knowledge_id)
);

-- 插入示例数据
INSERT INTO media_files (filename, original_name, file_path, file_size, mime_type, media_type, metadata) VALUES
('metal_coordinate_explanation.mp3', 'Metal坐标系解释.mp3', '/media/audio/metal_coordinate_explanation.mp3', 2048576, 'audio/mpeg', 'audio', 
 JSON_OBJECT('sample_rate', 44100, 'channels', 2, 'bitrate', 128)),
('depth_testing_demo.mp4', '深度测试演示.mp4', '/media/video/depth_testing_demo.mp4', 10485760, 'video/mp4', 'video',
 JSON_OBJECT('resolution', '1920x1080', 'fps', 30, 'codec', 'h264'));

-- 创建视图：完整的知识条目信息
CREATE VIEW knowledge_full_view AS
SELECT 
    ke.*,
    kc.name as category_name,
    COUNT(DISTINCT km.media_file_id) as media_count,
    COUNT(DISTINCT ce.id) as code_example_count,
    COUNT(DISTINCT d.id) as diagram_count,
    GROUP_CONCAT(DISTINCT mf.media_type) as available_media_types
FROM knowledge_entries ke
LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
LEFT JOIN knowledge_media km ON ke.id = km.knowledge_entry_id
LEFT JOIN media_files mf ON km.media_file_id = mf.id
LEFT JOIN code_examples ce ON ke.id = ce.knowledge_entry_id
LEFT JOIN diagrams d ON ke.id = d.knowledge_entry_id
GROUP BY ke.id;

-- 创建存储过程：智能搜索
DELIMITER //
CREATE PROCEDURE SmartSearch(
    IN search_query VARCHAR(500),
    IN include_audio BOOLEAN DEFAULT TRUE,
    IN include_video BOOLEAN DEFAULT TRUE,
    IN limit_count INT DEFAULT 20
)
BEGIN
    SELECT DISTINCT
        ke.id,
        ke.title,
        ke.summary,
        ke.difficulty_level,
        kc.name as category_name,
        ke.updated_at,
        CASE 
            WHEN ke.title LIKE CONCAT('%', search_query, '%') THEN 3.0
            WHEN ke.summary LIKE CONCAT('%', search_query, '%') THEN 2.0
            WHEN st.transcription_text LIKE CONCAT('%', search_query, '%') THEN 1.5
            ELSE 1.0
        END as relevance_score,
        GROUP_CONCAT(DISTINCT mf.media_type) as available_media
    FROM knowledge_entries ke
    LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
    LEFT JOIN knowledge_media km ON ke.id = km.knowledge_entry_id
    LEFT JOIN media_files mf ON km.media_file_id = mf.id
    LEFT JOIN speech_transcriptions st ON mf.id = st.media_file_id
    WHERE 
        (ke.title LIKE CONCAT('%', search_query, '%') OR
         ke.summary LIKE CONCAT('%', search_query, '%') OR
         ke.content LIKE CONCAT('%', search_query, '%') OR
         (include_audio AND st.transcription_text LIKE CONCAT('%', search_query, '%')))
    GROUP BY ke.id
    ORDER BY relevance_score DESC, ke.updated_at DESC
    LIMIT limit_count;
END //
DELIMITER ;

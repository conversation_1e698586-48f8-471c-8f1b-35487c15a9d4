<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🎵 音频播放测试</h1>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Metal深度测试语音解释</h3>
            </div>
            <div class="card-body">
                <p class="text-muted">这是一个AI生成的关于Metal深度测试的语音解释</p>
                
                <div class="audio-player mb-3">
                    <audio controls class="w-100" preload="metadata">
                        <source src="http://localhost:8081/media/1" type="audio/aiff">
                        <source src="http://localhost:8081/media/1" type="audio/x-aiff">
                        您的浏览器不支持音频播放。
                    </audio>
                </div>
                
                <div class="mt-3">
                    <h5>📝 语音转录内容：</h5>
                    <div class="bg-light p-3 rounded">
                        深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。
                        当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。
                        Metal使用深度缓冲区来存储每个像素的深度值，通过比较新像素和已存储像素的深度值来决定是否渲染。
                        这种机制确保了正确的3D视觉效果，让近处的对象能够正确遮挡远处的对象。
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>🔧 测试信息：</h6>
                    <ul>
                        <li>音频格式: AIFF (Apple音频格式)</li>
                        <li>文件大小: 约3.2MB</li>
                        <li>时长: 约45秒</li>
                        <li>语音: macOS系统中文语音 (Ting-Ting)</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <button onclick="testMediaUrl()" class="btn btn-primary">测试媒体URL</button>
                    <button onclick="downloadAudio()" class="btn btn-secondary">下载音频</button>
                </div>
                
                <div id="test-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>🛠️ 故障排除</h3>
            </div>
            <div class="card-body">
                <h6>如果听不到声音，请检查：</h6>
                <ol>
                    <li>浏览器是否支持AIFF格式（Safari最佳，Chrome和Firefox可能需要转换）</li>
                    <li>系统音量是否开启</li>
                    <li>浏览器是否允许自动播放音频</li>
                    <li>网络连接是否正常</li>
                </ol>
                
                <h6 class="mt-3">支持的浏览器：</h6>
                <ul>
                    <li>✅ Safari (macOS/iOS) - 完全支持AIFF</li>
                    <li>⚠️ Chrome - 可能需要转换为MP3</li>
                    <li>⚠️ Firefox - 可能需要转换为MP3</li>
                    <li>⚠️ Edge - 可能需要转换为MP3</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testMediaUrl() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="alert alert-info">正在测试媒体URL...</div>';
            
            fetch('http://localhost:8081/media/1')
                .then(response => {
                    if (response.ok) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                ✅ 媒体URL测试成功！<br>
                                状态码: ${response.status}<br>
                                内容类型: ${response.headers.get('content-type')}<br>
                                内容长度: ${response.headers.get('content-length')} 字节
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                ❌ 媒体URL测试失败！<br>
                                状态码: ${response.status}<br>
                                错误: ${response.statusText}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            ❌ 网络错误: ${error.message}
                        </div>
                    `;
                });
        }
        
        function downloadAudio() {
            const link = document.createElement('a');
            link.href = 'http://localhost:8081/media/1';
            link.download = 'metal_depth_testing_explanation.aiff';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            setTimeout(testMediaUrl, 1000);
        };
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新现有文件的文件夹路径
从文件的原始名称中提取文件夹信息并更新到数据库
"""

import mysql.connector
import json
import os
from pathlib import Path

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def extract_folder_path(original_name, metadata_str):
    """从原始文件名和metadata中提取文件夹路径"""
    try:
        # 尝试从metadata中获取
        if metadata_str:
            metadata = json.loads(metadata_str)
            if 'folder_path' in metadata:
                return metadata['folder_path']
        
        # 从原始文件名中提取路径
        if '/' in original_name:
            # Unix风格路径
            path_parts = original_name.split('/')
            if len(path_parts) > 1:
                return '/'.join(path_parts[:-1])
        elif '\\' in original_name:
            # Windows风格路径
            path_parts = original_name.split('\\')
            if len(path_parts) > 1:
                return '/'.join(path_parts[:-1])  # 统一使用/作为分隔符
        
        return ""  # 根目录
    except Exception as e:
        print(f"提取路径失败: {e}")
        return ""

def update_folder_paths():
    """更新所有文件的文件夹路径"""
    conn = get_db_connection()
    if not conn:
        print("❌ 数据库连接失败")
        return
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # 获取所有需要更新的文件
        cursor.execute("""
            SELECT id, original_name, metadata 
            FROM media_files 
            WHERE folder_path IS NULL OR folder_path = ''
        """)
        files = cursor.fetchall()
        
        print(f"📁 找到 {len(files)} 个文件需要更新文件夹路径")
        
        updated_count = 0
        for file in files:
            folder_path = extract_folder_path(file['original_name'], file['metadata'])
            
            if folder_path:
                # 更新数据库
                cursor.execute("""
                    UPDATE media_files 
                    SET folder_path = %s 
                    WHERE id = %s
                """, (folder_path, file['id']))
                
                print(f"✅ 更新文件 {file['id']}: {file['original_name']} -> {folder_path}")
                updated_count += 1
            else:
                print(f"📄 文件 {file['id']}: {file['original_name']} (根目录)")
        
        # 提交更改
        conn.commit()
        print(f"\n🎉 成功更新了 {updated_count} 个文件的文件夹路径！")
        
        # 显示统计信息
        cursor.execute("""
            SELECT folder_path, COUNT(*) as count 
            FROM media_files 
            WHERE folder_path != '' AND folder_path IS NOT NULL
            GROUP BY folder_path 
            ORDER BY count DESC
        """)
        folder_stats = cursor.fetchall()
        
        print("\n📊 文件夹统计:")
        for stat in folder_stats:
            print(f"  📂 {stat['folder_path']}: {stat['count']} 个文件")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        if conn:
            conn.rollback()

if __name__ == "__main__":
    print("🚀 开始更新文件夹路径...")
    update_folder_paths()
    print("✅ 更新完成！")

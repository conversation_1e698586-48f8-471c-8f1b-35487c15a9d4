#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级安全防护中间件演示
模拟WAF（Web应用防火墙）和参数过滤系统
"""

import re
import json
import html
import urllib.parse
from typing import Dict, List, Any, Tuple
from datetime import datetime

class SecurityFilter:
    """安全过滤器 - 模拟企业级WAF功能"""
    
    def __init__(self):
        self.blocked_patterns = [
            # SQL注入模式
            r"(union|select|insert|delete|update|drop|create|alter)\s+",
            r"(\bor\b|\band\b)\s+\d+\s*=\s*\d+",
            r"['\";]+.*['\";]+",
            
            # XSS攻击模式  
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            
            # 命令注入模式
            r"[;&|`$(){}[\]\\]",
            r"(cat|ls|rm|cp|mv|chmod|sudo)\s+",
            
            # 路径遍历
            r"\.\./",
            r"\\\\",
        ]
        
        self.rate_limit = {}  # IP访问频率限制
        self.suspicious_ips = set()  # 可疑IP列表
    
    def analyze_request(self, request_data: Dict) -> Tuple[bool, str, Dict]:
        """分析请求安全性"""
        print(f"🔍 分析请求: {request_data.get('endpoint', 'unknown')}")
        
        # 1. IP黑名单检查
        client_ip = request_data.get('client_ip', '127.0.0.1')
        if self._is_blocked_ip(client_ip):
            return False, "IP已被封禁", {}
        
        # 2. 频率限制检查
        if self._check_rate_limit(client_ip):
            return False, "请求频率过高", {}
        
        # 3. 参数内容过滤
        clean_params, threats = self._filter_parameters(request_data.get('params', {}))
        
        if threats:
            self._log_security_event(client_ip, threats)
            return False, f"检测到安全威胁: {', '.join(threats)}", {}
        
        # 4. 返回清理后的参数
        return True, "请求安全", clean_params
    
    def _filter_parameters(self, params: Dict) -> Tuple[Dict, List[str]]:
        """过滤和清理请求参数"""
        clean_params = {}
        detected_threats = []
        
        for key, value in params.items():
            if isinstance(value, str):
                # 检查恶意模式
                for pattern in self.blocked_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        detected_threats.append(f"恶意模式在{key}: {pattern}")
                        continue
                
                # 清理参数值
                clean_value = self._sanitize_input(value)
                clean_params[key] = clean_value
            else:
                clean_params[key] = value
        
        return clean_params, detected_threats
    
    def _sanitize_input(self, input_str: str) -> str:
        """输入清理"""
        # HTML转义
        cleaned = html.escape(input_str)
        
        # URL解码
        cleaned = urllib.parse.unquote(cleaned)
        
        # 移除危险字符
        cleaned = re.sub(r'[<>"\'\`]', '', cleaned)
        
        # 限制长度
        if len(cleaned) > 1000:
            cleaned = cleaned[:1000]
        
        return cleaned.strip()
    
    def _is_blocked_ip(self, ip: str) -> bool:
        """检查IP是否被封禁"""
        return ip in self.suspicious_ips
    
    def _check_rate_limit(self, ip: str) -> bool:
        """检查访问频率"""
        now = datetime.now()
        
        if ip not in self.rate_limit:
            self.rate_limit[ip] = []
        
        # 移除1分钟前的记录
        self.rate_limit[ip] = [
            timestamp for timestamp in self.rate_limit[ip] 
            if (now - timestamp).seconds < 60
        ]
        
        # 检查是否超过限制（每分钟100次请求）
        if len(self.rate_limit[ip]) > 100:
            self.suspicious_ips.add(ip)
            return True
        
        self.rate_limit[ip].append(now)
        return False
    
    def _log_security_event(self, ip: str, threats: List[str]):
        """记录安全事件"""
        event = {
            'timestamp': datetime.now().isoformat(),
            'client_ip': ip,
            'threats': threats,
            'action': 'blocked'
        }
        print(f"🚨 安全告警: {json.dumps(event, ensure_ascii=False, indent=2)}")


class DatabaseSecurityGateway:
    """数据库安全网关"""
    
    def __init__(self):
        self.security_filter = SecurityFilter()
        self.allowed_operations = ['SELECT', 'INSERT', 'UPDATE']  # 禁止DELETE/DROP
        self.parameter_whitelist = {
            'knowledge_search': ['query', 'category', 'difficulty'],
            'knowledge_create': ['title', 'content', 'summary', 'tags'],
            'user_auth': ['username', 'password']
        }
    
    def process_database_request(self, operation: str, endpoint: str, 
                               params: Dict, client_ip: str = '127.0.0.1') -> Dict:
        """处理数据库请求"""
        print(f"\n🛡️  === 安全网关处理请求 ===")
        
        # 构造请求数据
        request_data = {
            'endpoint': endpoint,
            'params': params,
            'client_ip': client_ip,
            'operation': operation
        }
        
        # 1. 安全过滤
        is_safe, message, clean_params = self.security_filter.analyze_request(request_data)
        
        if not is_safe:
            return {
                'success': False,
                'error': message,
                'blocked': True
            }
        
        # 2. 操作权限检查
        if operation.upper() not in self.allowed_operations:
            return {
                'success': False,
                'error': f"操作 {operation} 不被允许",
                'blocked': True
            }
        
        # 3. 参数白名单验证
        if endpoint in self.parameter_whitelist:
            allowed_params = self.parameter_whitelist[endpoint]
            filtered_params = {
                k: v for k, v in clean_params.items() 
                if k in allowed_params
            }
        else:
            filtered_params = clean_params
        
        # 4. 执行数据库操作（模拟）
        result = self._execute_safe_database_operation(operation, endpoint, filtered_params)
        
        print(f"✅ 请求处理完成: {endpoint}")
        return result
    
    def _execute_safe_database_operation(self, operation: str, endpoint: str, params: Dict) -> Dict:
        """安全的数据库操作执行"""
        # 这里模拟真实的数据库操作
        print(f"📊 执行数据库操作:")
        print(f"   操作: {operation}")
        print(f"   端点: {endpoint}")
        print(f"   清理后参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        # 模拟成功返回
        return {
            'success': True,
            'data': f"模拟 {operation} 操作结果",
            'processed_params': params,
            'timestamp': datetime.now().isoformat()
        }


# 演示企业级安全防护
def demo_enterprise_security():
    """演示企业级安全防护系统"""
    print("🏢 企业级数据库安全防护演示\n")
    
    gateway = DatabaseSecurityGateway()
    
    # 测试案例
    test_cases = [
        {
            'name': '正常查询请求',
            'operation': 'SELECT',
            'endpoint': 'knowledge_search',
            'params': {'query': 'Metal编程', 'category': '技术'},
            'client_ip': '*************'
        },
        {
            'name': 'SQL注入攻击',
            'operation': 'SELECT', 
            'endpoint': 'knowledge_search',
            'params': {'query': "'; DROP TABLE users; --", 'category': '技术'},
            'client_ip': '*************'
        },
        {
            'name': 'XSS攻击尝试',
            'operation': 'INSERT',
            'endpoint': 'knowledge_create',
            'params': {
                'title': '<script>alert("hack")</script>',
                'content': '正常内容'
            },
            'client_ip': '*************'
        },
        {
            'name': '危险操作尝试',
            'operation': 'DROP',
            'endpoint': 'admin_operation', 
            'params': {'table': 'knowledge_entries'},
            'client_ip': '*************'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试案例 {i}: {case['name']}")
        print("-" * 50)
        
        result = gateway.process_database_request(
            case['operation'],
            case['endpoint'], 
            case['params'],
            case['client_ip']
        )
        
        if result['success']:
            print(f"✅ 请求通过安全检查")
        else:
            print(f"❌ 请求被拦截: {result['error']}")
        
        print()


if __name__ == "__main__":
    demo_enterprise_security()
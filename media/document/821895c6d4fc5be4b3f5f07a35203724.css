/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: white;
  color: black;
}

#root {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

/* 确保Three.js画布正确显示 */
.three-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.three-container canvas {
  width: 100% !important;
  height: 100% !important;
}
{"name": "Earth & Solar System Dynamics", "description": "A dual simulation: 1) A 3D self-rotating Earth with detailed textures rendered using Three.js. 2) A model of Earth revolving around the Sun, rendered using WebGL for celestial bodies and SVG for the orbit and seasonal text. Animations are designed for smoothness and handle window visibility changes.", "requestFramePermissions": [], "prompt": ""}
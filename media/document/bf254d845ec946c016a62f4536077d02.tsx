import React, { useRef, useEffect, useCallback } from 'react';
import * as THREE from 'three';

// 使用简单可靠的地球纹理
const SIMPLE_EARTH_TEXTURE = 'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg';

const EarthFallback: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);

  const sceneRef = useRef(new THREE.Scene());
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const earthMeshRef = useRef<THREE.Mesh | null>(null);

  const setupScene = useCallback((container: HTMLDivElement) => {
    const width = container.clientWidth;
    const height = container.clientHeight;

    console.log('设置备用地球场景，尺寸:', width, height);

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.setClearColor(0x000011, 1);
    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Camera
    const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    camera.position.set(0, 0, 3.5);
    cameraRef.current = camera;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    sceneRef.current.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(5, 3, 5);
    sceneRef.current.add(directionalLight);

    // Earth geometry - 使用简单配置
    const geometry = new THREE.SphereGeometry(1, 32, 32);

    // 创建纯色材质作为初始状态
    const material = new THREE.MeshPhongMaterial({
      color: 0x4488cc,
      specular: 0x111111,
      shininess: 5
    });

    const earthMesh = new THREE.Mesh(geometry, material);
    earthMesh.rotation.y = Math.PI;
    sceneRef.current.add(earthMesh);
    earthMeshRef.current = earthMesh;

    // 加载纹理
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(
      SIMPLE_EARTH_TEXTURE,
      (texture) => {
        console.log('备用地球纹理加载成功');
        texture.colorSpace = THREE.SRGBColorSpace;
        
        // 创建新材质
        const newMaterial = new THREE.MeshPhongMaterial({
          map: texture,
          color: 0xffffff,
          specular: 0x111111,
          shininess: 5
        });

        // 更新材质
        if (earthMeshRef.current) {
          earthMeshRef.current.material = newMaterial;
          material.dispose(); // 清理旧材质
        }
      },
      undefined,
      (error) => {
        console.error('备用地球纹理加载失败:', error);
      }
    );

  }, []);

  const animate = useCallback(() => {
    animationFrameIdRef.current = requestAnimationFrame(animate);
    if (earthMeshRef.current) {
      earthMeshRef.current.rotation.y += 0.002;
    }
    if (rendererRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  }, []);

  const handleResize = useCallback(() => {
    if (mountRef.current && rendererRef.current && cameraRef.current) {
      const container = mountRef.current;
      const width = container.clientWidth;
      const height = container.clientHeight;

      rendererRef.current.setSize(width, height);
      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
    }
  }, []);

  useEffect(() => {
    const currentMountRef = mountRef.current;
    if (currentMountRef && !rendererRef.current) {
        setupScene(currentMountRef);
        animate();
    }
    
    window.addEventListener('resize', handleResize);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      
      // 清理Three.js对象
      if (rendererRef.current) {
        rendererRef.current.dispose();
        if (rendererRef.current.domElement.parentNode === currentMountRef) {
             currentMountRef?.removeChild(rendererRef.current.domElement);
        }
      }
      sceneRef.current.traverse(object => {
        if (object instanceof THREE.Mesh) {
          object.geometry?.dispose();
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material?.dispose();
          }
        }
      });
      rendererRef.current = null;
      cameraRef.current = null;
      earthMeshRef.current = null;
    };
  }, [setupScene, animate, handleResize]);

  return <div ref={mountRef} className="w-full h-full" />;
};

export default EarthFallback;
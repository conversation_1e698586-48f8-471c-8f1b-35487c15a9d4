<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}技术知识管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Prism.js for code highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <!-- Mermaid for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    
    <style>
        .knowledge-card {
            transition: transform 0.2s;
        }
        .knowledge-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .tag {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
            display: inline-block;
        }
        .difficulty-badge {
            font-size: 0.75rem;
        }
        .code-example {
            margin: 1rem 0;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .code-header {
            background-color: #f8f9fa;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
        }
        .mermaid-diagram {
            text-align: center;
            margin: 1rem 0;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
        .search-filters {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-brain"></i> 技术知识库
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">搜索</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('categories') }}">分类浏览</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('learning_paths') }}">学习路径</a>
                    </li>
                </ul>
                
                <!-- 快速搜索 -->
                <form class="d-flex" action="{{ url_for('search') }}" method="GET">
                    <input class="form-control me-2" type="search" name="q" placeholder="搜索知识..." 
                           value="{{ request.args.get('q', '') }}">
                    <button class="btn btn-outline-light" type="submit">搜索</button>
                </form>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">技术知识管理系统 - 让学习更有序</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for code highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    
    <script>
        // 初始化Mermaid
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
        
        // 动态加载Mermaid图表
        function renderMermaidDiagrams() {
            const diagrams = document.querySelectorAll('.mermaid-code');
            diagrams.forEach((diagram, index) => {
                const code = diagram.textContent;
                const container = document.createElement('div');
                container.className = 'mermaid-diagram';
                container.id = `mermaid-${index}`;
                
                mermaid.render(`mermaid-svg-${index}`, code, (svgCode) => {
                    container.innerHTML = svgCode;
                });
                
                diagram.parentNode.replaceChild(container, diagram);
            });
        }
        
        // 页面加载完成后渲染图表
        document.addEventListener('DOMContentLoaded', function() {
            renderMermaidDiagrams();
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>2007年版本的技术知识管理系统</title>
    
    <!-- 2007年的CSS样式 -->
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .container {
            width: 800px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ccc;
        }
        
        .header {
            background-color: #336699;
            color: white;
            padding: 10px;
            text-align: center;
        }
        
        .nav {
            background-color: #e6f2ff;
            padding: 5px;
            border-bottom: 1px solid #ccc;
        }
        
        .nav a {
            color: #336699;
            text-decoration: none;
            margin-right: 15px;
            font-weight: bold;
        }
        
        .nav a:hover {
            text-decoration: underline;
        }
        
        .content {
            padding: 20px;
            float: left;
            width: 500px;
        }
        
        .sidebar {
            float: right;
            width: 250px;
            padding: 20px;
            background-color: #f9f9f9;
            border-left: 1px solid #ddd;
        }
        
        .knowledge-item {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #fafafa;
        }
        
        .knowledge-item h3 {
            margin: 0 0 10px 0;
            color: #336699;
        }
        
        .knowledge-item p {
            margin: 5px 0;
            color: #666;
        }
        
        .tag {
            background-color: #e0e0e0;
            padding: 2px 5px;
            font-size: 11px;
            margin-right: 5px;
        }
        
        .clear {
            clear: both;
        }
        
        .footer {
            background-color: #336699;
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 12px;
        }
        
        /* 2007年没有的现代CSS特性 */
        /* 
        .modern-features {
            border-radius: 5px;          // CSS3 - 2007年不支持
            box-shadow: 0 2px 5px #ccc;  // CSS3 - 2007年不支持
            transform: rotate(5deg);     // CSS3 - 2007年不支持
            display: flex;               // CSS3 - 2007年不支持
        }
        */
    </style>
    
    <!-- 2007年的JavaScript -->
    <script type="text/javascript">
        // 2007年可用的JavaScript功能
        function showKnowledgeDetail(id) {
            // 使用document.getElementById (1998年就有了)
            var element = document.getElementById('knowledge-' + id);
            if (element.style.display == 'none') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }
        
        function validateForm() {
            // 基本的表单验证
            var title = document.forms["knowledgeForm"]["title"].value;
            if (title == "") {
                alert("请输入标题！");
                return false;
            }
            return true;
        }
        
        // 2007年的AJAX (XMLHttpRequest - 2000年就有了)
        function loadKnowledge() {
            var xmlhttp;
            if (window.XMLHttpRequest) {
                // IE7+, Firefox, Chrome, Opera, Safari
                xmlhttp = new XMLHttpRequest();
            } else {
                // IE6, IE5
                xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
            }
            
            xmlhttp.onreadystatechange = function() {
                if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                    document.getElementById("knowledge-list").innerHTML = xmlhttp.responseText;
                }
            }
            xmlhttp.open("GET", "knowledge.php", true);
            xmlhttp.send();
        }
        
        // 2007年不支持的现代JavaScript特性
        /*
        // ES6 箭头函数 (2015年)
        const loadData = () => {
            fetch('/api/data')  // Fetch API (2015年)
                .then(response => response.json())
                .then(data => console.log(data));
        };
        
        // ES6 const/let (2015年)
        const modernFeature = "不支持";
        
        // ES6 模板字符串 (2015年)
        const message = `Hello ${name}`;
        */
    </script>
</head>

<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>技术知识管理系统 - 2007年版本</h1>
            <p>兼容 Internet Explorer 6/7, Firefox 2.0</p>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav">
            <a href="#" onclick="loadKnowledge(); return false;">首页</a>
            <a href="#" onclick="alert('搜索功能'); return false;">搜索</a>
            <a href="#" onclick="alert('分类浏览'); return false;">分类</a>
            <a href="#" onclick="alert('帮助信息'); return false;">帮助</a>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="content">
            <h2>最新知识条目</h2>
            
            <div class="knowledge-item">
                <h3><a href="#" onclick="showKnowledgeDetail(1); return false;">Metal 3D坐标系详解</a></h3>
                <p>详细解释Metal中3D坐标系的设计原理...</p>
                <div class="tag">Metal编程</div>
                <div class="tag">3D图形</div>
                <div class="tag">坐标系</div>
                
                <!-- 隐藏的详细内容 -->
                <div id="knowledge-1" style="display: none;">
                    <hr>
                    <h4>详细内容：</h4>
                    <p><strong>Metal 3D坐标系是3D图形渲染的基础概念。</strong></p>
                    <p>在Metal中，坐标系变换遵循标准的3D图形管线：</p>
                    <ul>
                        <li>本地坐标 → 世界坐标</li>
                        <li>世界坐标 → 视图坐标</li>
                        <li>视图坐标 → 屏幕坐标</li>
                    </ul>
                    
                    <!-- 2007年的代码展示方式 -->
                    <p><strong>代码示例：</strong></p>
                    <pre style="background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc;">
// Metal着色器代码 (2007年还没有Metal，这只是示例)
vertex VertexOut sphere_vertex(Vertex3D in [[stage_in]]) {
    VertexOut out;
    out.position = mvpMatrix * float4(in.position, 1.0);
    return out;
}</pre>
                    
                    <!-- 2007年不能播放音频/视频 -->
                    <p><strong>多媒体内容：</strong></p>
                    <p style="color: red;">❌ 音频播放：需要安装Flash插件或Windows Media Player插件</p>
                    <p style="color: red;">❌ 视频播放：需要安装QuickTime或RealPlayer插件</p>
                    
                    <!-- 2007年的"图表"展示方式 -->
                    <p><strong>流程图：</strong></p>
                    <table border="1" cellpadding="5" style="border-collapse: collapse;">
                        <tr>
                            <td>本地坐标</td>
                            <td>→</td>
                            <td>世界坐标</td>
                            <td>→</td>
                            <td>视图坐标</td>
                            <td>→</td>
                            <td>屏幕坐标</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="knowledge-item">
                <h3><a href="#" onclick="showKnowledgeDetail(2); return false;">深度测试原理</a></h3>
                <p>深度测试是3D图形渲染中的核心技术...</p>
                <div class="tag">深度测试</div>
                <div class="tag">3D渲染</div>
                
                <div id="knowledge-2" style="display: none;">
                    <hr>
                    <p>深度测试用于确定哪些像素应该被渲染到屏幕上。</p>
                    <p>工作原理：比较像素的Z值，近的遮挡远的。</p>
                </div>
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div class="sidebar">
            <h3>系统信息</h3>
            <p>✅ 支持 Internet Explorer 6+</p>
            <p>✅ 支持 Firefox 2.0+</p>
            <p>✅ 基本JavaScript功能</p>
            <p>❌ 不支持HTML5音视频</p>
            <p>❌ 不支持CSS3特效</p>
            
            <h3>技术限制</h3>
            <ul style="font-size: 12px;">
                <li>无法播放MP3/MP4文件</li>
                <li>需要Flash插件播放媒体</li>
                <li>不支持拖拽上传</li>
                <li>不支持现代JavaScript</li>
                <li>布局使用float而非flexbox</li>
            </ul>
            
            <h3>添加知识</h3>
            <form name="knowledgeForm" onsubmit="return validateForm()">
                <p>标题：<br><input type="text" name="title" style="width: 200px;"></p>
                <p>内容：<br><textarea name="content" rows="4" cols="25"></textarea></p>
                <p><input type="submit" value="提交"></p>
            </form>
        </div>
        
        <div class="clear"></div>
        
        <!-- 页脚 -->
        <div class="footer">
            <p>技术知识管理系统 2007年版本 | 兼容IE6+ | 需要JavaScript支持</p>
        </div>
    </div>
    
    <!-- 2007年的页面加载完成事件 -->
    <script type="text/javascript">
        // 页面加载完成后执行
        window.onload = function() {
            alert("欢迎使用2007年版本的知识管理系统！\n\n支持的功能：\n- 基本的HTML表单\n- 简单的JavaScript交互\n- AJAX数据加载\n\n不支持的功能：\n- HTML5音视频播放\n- CSS3动画效果\n- 现代JavaScript语法");
        };
    </script>
</body>
</html>

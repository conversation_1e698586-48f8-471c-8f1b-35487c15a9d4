#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术知识管理Web应用
基于Flask的知识展示和搜索界面
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from knowledge_manager import KnowledgeManager
import json
import markdown
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import HtmlFormatter
import re

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# 初始化知识管理器
km = KnowledgeManager()

@app.route('/')
def index():
    """首页 - 显示最新的知识条目"""
    try:
        # 获取最新的10个知识条目
        query = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        ORDER BY ke.updated_at DESC
        LIMIT 10
        """
        km.cursor.execute(query)
        recent_entries = km.cursor.fetchall()
        
        # 获取所有分类
        km.cursor.execute("SELECT * FROM knowledge_categories WHERE parent_id IS NULL")
        categories = km.cursor.fetchall()
        
        return render_template('index.html', 
                             recent_entries=recent_entries, 
                             categories=categories)
    except Exception as e:
        return f"错误: {str(e)}"

@app.route('/search')
def search():
    """搜索页面"""
    query = request.args.get('q', '')
    search_type = request.args.get('type', 'fulltext')
    category = request.args.get('category', '')
    
    results = []
    if query:
        if search_type == 'tag':
            tags = [tag.strip() for tag in query.split(',')]
            results = km.search_knowledge('', search_type='tag', tags=tags)
        else:
            results = km.search_knowledge(query, search_type=search_type, category=category)
    
    # 获取所有分类用于筛选
    km.cursor.execute("SELECT * FROM knowledge_categories")
    categories = km.cursor.fetchall()
    
    return render_template('search.html', 
                         results=results, 
                         query=query, 
                         search_type=search_type,
                         categories=categories,
                         selected_category=category)

@app.route('/knowledge/<int:knowledge_id>')
def knowledge_detail(knowledge_id):
    """知识详情页面"""
    entry = km.get_knowledge_with_examples(knowledge_id)
    if not entry:
        return "知识条目不存在", 404
    
    # 处理Markdown内容
    entry['content_html'] = markdown.markdown(entry['content'], extensions=['codehilite', 'fenced_code'])
    
    # 处理代码高亮
    for code_example in entry.get('code_examples', []):
        try:
            lexer = get_lexer_by_name(code_example['language'])
            formatter = HtmlFormatter(style='github', linenos=True)
            code_example['highlighted_code'] = highlight(code_example['code_content'], lexer, formatter)
        except:
            code_example['highlighted_code'] = f"<pre><code>{code_example['code_content']}</code></pre>"
    
    return render_template('knowledge_detail.html', entry=entry)

@app.route('/api/save_knowledge', methods=['POST'])
def api_save_knowledge():
    """API: 保存新的知识条目"""
    try:
        data = request.json
        
        knowledge_id = km.save_knowledge_entry(
            title=data['title'],
            content=data['content'],
            summary=data.get('summary', ''),
            category_name=data.get('category', ''),
            tags=data.get('tags', []),
            difficulty=data.get('difficulty', 'intermediate'),
            source_type=data.get('source_type', 'ai_explanation')
        )
        
        # 保存代码示例
        for code in data.get('code_examples', []):
            km.save_code_example(
                knowledge_entry_id=knowledge_id,
                title=code['title'],
                language=code['language'],
                code_content=code['code_content'],
                description=code.get('description', ''),
                file_path=code.get('file_path', '')
            )
        
        # 保存图表
        for diagram in data.get('diagrams', []):
            km.save_diagram(
                knowledge_entry_id=knowledge_id,
                title=diagram['title'],
                diagram_definition=diagram['diagram_definition'],
                diagram_type=diagram.get('diagram_type', 'mermaid'),
                description=diagram.get('description', '')
            )
        
        return jsonify({'success': True, 'knowledge_id': knowledge_id})
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search')
def api_search():
    """API: 搜索知识"""
    query = request.args.get('q', '')
    search_type = request.args.get('type', 'fulltext')
    category = request.args.get('category', '')
    
    try:
        if search_type == 'tag':
            tags = [tag.strip() for tag in query.split(',')]
            results = km.search_knowledge('', search_type='tag', tags=tags)
        else:
            results = km.search_knowledge(query, search_type=search_type, category=category)
        
        return jsonify({'success': True, 'results': results})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/learning_paths')
def learning_paths():
    """学习路径列表"""
    km.cursor.execute("SELECT * FROM learning_paths ORDER BY created_at DESC")
    paths = km.cursor.fetchall()
    return render_template('learning_paths.html', paths=paths)

@app.route('/learning_path/<int:path_id>')
def learning_path_detail(path_id):
    """学习路径详情"""
    path = km.get_learning_path(path_id)
    if not path:
        return "学习路径不存在", 404
    
    return render_template('learning_path_detail.html', path=path)

@app.route('/categories')
def categories():
    """分类浏览"""
    km.cursor.execute("""
        SELECT kc.*, COUNT(ke.id) as entry_count
        FROM knowledge_categories kc
        LEFT JOIN knowledge_entries ke ON kc.id = ke.category_id
        GROUP BY kc.id
        ORDER BY kc.name
    """)
    categories = km.cursor.fetchall()
    return render_template('categories.html', categories=categories)

@app.route('/category/<int:category_id>')
def category_detail(category_id):
    """分类详情"""
    # 获取分类信息
    km.cursor.execute("SELECT * FROM knowledge_categories WHERE id = %s", (category_id,))
    category = km.cursor.fetchone()
    
    if not category:
        return "分类不存在", 404
    
    # 获取该分类下的知识条目
    km.cursor.execute("""
        SELECT * FROM knowledge_entries 
        WHERE category_id = %s 
        ORDER BY updated_at DESC
    """, (category_id,))
    entries = km.cursor.fetchall()
    
    return render_template('category_detail.html', category=category, entries=entries)

# 模板过滤器
@app.template_filter('truncate_words')
def truncate_words(text, length=50):
    """截断文本到指定字数"""
    if len(text) <= length:
        return text
    return text[:length] + '...'

@app.template_filter('format_tags')
def format_tags(tags_json):
    """格式化标签JSON为列表"""
    try:
        if isinstance(tags_json, str):
            return json.loads(tags_json)
        return tags_json or []
    except:
        return []

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音知识保存演示
展示如何将AI助手的语音解释保存到知识库
"""

import mysql.connector
import json
import os
from datetime import datetime

def setup_multimedia_database():
    """设置多媒体数据库表"""
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        
        print("🔧 正在设置多媒体数据库表...")
        
        # 执行多媒体数据库架构
        with open('multimedia_knowledge_schema.sql', 'r', encoding='utf-8') as f:
            sql_commands = f.read()
        
        # 分割SQL命令并执行
        commands = sql_commands.split(';')
        for command in commands:
            command = command.strip()
            if command and not command.startswith('--'):
                try:
                    cursor.execute(command)
                except mysql.connector.Error as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠️  SQL执行警告: {e}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ 多媒体数据库表设置完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库设置失败: {e}")
        return False

def create_demo_audio_file():
    """创建演示音频文件（模拟）"""
    # 创建媒体目录
    os.makedirs("media/audio", exist_ok=True)
    
    # 创建一个模拟的音频文件信息
    demo_audio_info = {
        "filename": "metal_depth_testing_explanation.mp3",
        "content": "这是一个关于Metal深度测试的语音解释。深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。",
        "duration": 45.5,
        "transcription": "深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。Metal使用深度缓冲区来存储每个像素的深度值，通过比较新像素和已存储像素的深度值来决定是否渲染。"
    }
    
    return demo_audio_info

def save_voice_knowledge_demo():
    """保存语音知识演示"""
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor(dictionary=True)
        
        print("🎵 开始保存语音知识演示...")
        
        # 1. 创建演示音频信息
        audio_info = create_demo_audio_file()
        
        # 2. 保存媒体文件记录
        media_query = """
        INSERT INTO media_files 
        (filename, original_name, file_path, file_size, mime_type, media_type, 
         duration_seconds, file_hash, metadata)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        metadata = {
            "sample_rate": 44100,
            "channels": 2,
            "bitrate": 128,
            "description": "AI助手关于Metal深度测试的语音解释"
        }
        
        cursor.execute(media_query, (
            audio_info["filename"],
            "Metal深度测试语音解释.mp3",
            f"media/audio/{audio_info['filename']}",
            1024000,  # 1MB 模拟文件大小
            "audio/mpeg",
            "audio",
            audio_info["duration"],
            "demo_hash_12345",
            json.dumps(metadata, ensure_ascii=False)
        ))
        
        media_id = cursor.lastrowid
        print(f"✅ 媒体文件已保存，ID: {media_id}")
        
        # 3. 保存语音转录
        transcription_query = """
        INSERT INTO speech_transcriptions 
        (media_file_id, transcription_text, confidence_score, language, transcription_engine)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        cursor.execute(transcription_query, (
            media_id,
            audio_info["transcription"],
            0.95,  # 95% 置信度
            "zh-CN",
            "demo_engine"
        ))
        
        print("✅ 语音转录已保存")
        
        # 4. 关联到现有的深度测试知识条目
        cursor.execute("SELECT id FROM knowledge_entries WHERE title LIKE '%深度测试%' LIMIT 1")
        knowledge_entry = cursor.fetchone()
        
        if knowledge_entry:
            knowledge_id = knowledge_entry['id']
            
            # 关联媒体到知识条目
            relation_query = """
            INSERT INTO knowledge_media 
            (knowledge_entry_id, media_file_id, media_role, description)
            VALUES (%s, %s, %s, %s)
            """
            
            cursor.execute(relation_query, (
                knowledge_id,
                media_id,
                "explanation_audio",
                "AI助手对深度测试概念的详细语音解释"
            ))
            
            print(f"✅ 语音已关联到知识条目 ID: {knowledge_id}")
        
        # 5. 添加音频注释示例
        annotation_query = """
        INSERT INTO audio_annotations 
        (media_file_id, start_time, end_time, annotation_text, annotation_type)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        annotations = [
            (5.0, 15.0, "深度测试的基本概念介绍", "highlight"),
            (20.0, 30.0, "Metal中的具体实现方法", "note"),
            (35.0, 45.0, "性能优化技巧", "summary")
        ]
        
        for start, end, text, ann_type in annotations:
            cursor.execute(annotation_query, (media_id, start, end, text, ann_type))
        
        print("✅ 音频注释已添加")
        
        # 6. 更新搜索索引
        if knowledge_entry:
            search_index_query = """
            INSERT INTO knowledge_search_index 
            (knowledge_entry_id, content_type, searchable_content, content_weight)
            VALUES (%s, %s, %s, %s)
            """
            
            cursor.execute(search_index_query, (
                knowledge_id,
                "audio_transcript",
                audio_info["transcription"],
                1.2  # 语音内容权重稍高
            ))
            
            print("✅ 搜索索引已更新")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 语音知识保存演示完成！")
        print("现在您可以:")
        print("1. 在Web界面中看到语音播放器")
        print("2. 查看语音转录文本")
        print("3. 搜索语音内容")
        print("4. 查看音频注释")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def show_multimedia_capabilities():
    """展示多媒体功能"""
    print("\n🌟 多媒体知识管理系统功能展示")
    print("=" * 50)
    
    print("\n📱 支持的媒体类型:")
    print("🎵 音频文件: MP3, WAV, M4A")
    print("🎬 视频文件: MP4, AVI, MOV")
    print("🖼️  图片文件: JPG, PNG, GIF")
    print("📄 文档文件: PDF, DOC, TXT")
    
    print("\n🧠 智能处理功能:")
    print("🗣️  语音转文字: 自动转录音频内容")
    print("👁️  图像识别: OCR提取图片中的文字")
    print("🔍 多维搜索: 在文字、语音、图片中搜索")
    print("⏰ 时间注释: 为音频/视频添加时间点注释")
    
    print("\n💡 高维度知识特性:")
    print("🎭 情感分析: 分析语音中的情感和语调")
    print("🔗 知识关联: 自动发现知识之间的关系")
    print("📊 学习跟踪: 记录学习进度和时间")
    print("🎯 个性推荐: 基于学习历史推荐相关内容")
    
    print("\n🚀 使用场景:")
    print("📚 保存AI助手的语音解释")
    print("🎤 录制自己的学习笔记")
    print("📹 保存技术演示视频")
    print("📸 保存代码截图和图表")
    print("🔄 建立知识之间的关联")

def main():
    """主函数"""
    print("🎵 多媒体知识管理系统设置")
    print("=" * 40)
    
    # 1. 设置数据库
    if not setup_multimedia_database():
        return
    
    # 2. 保存演示语音知识
    if not save_voice_knowledge_demo():
        return
    
    # 3. 展示功能
    show_multimedia_capabilities()
    
    print("\n🌐 重启Web应用以查看多媒体功能:")
    print("python3 simple_app.py")
    print("访问 http://localhost:8080")

if __name__ == "__main__":
    main()

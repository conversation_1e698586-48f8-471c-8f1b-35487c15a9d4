import React, { useRef, useEffect, useMemo } from 'react';
import {
  TERM_NAMES,
  TERM_TO_MONTH,
  OBLIQ_DEG, // Used by insolationFraction within component
  AXIS_MARGIN,
  getSolarLongitudeDeg,
  calculateDaylightHours,
  getSolarTermIndexFromLs,
  setupTrackGeometry,
  TRACK_GEOMETRY_PARAMS,
  TRACK_TRANSFORM_PARAMS,
  getTrackLocalPositionTestEarth2,
  transformTrackLocalToCanvasTestEarth2,
  mapSolarLongitudeToTrackRatioTestEarth2, // Updated import name
} from '../utils/insolationChartUtils';

interface InsolationChartProps {
  currentOrbitalAngleRad: number;
}

const CHART_CANVAS_WIDTH = 640; 
const CHART_CANVAS_HEIGHT = 800; 
const TRACK_VISUAL_HEIGHT = 15; 

const InsolationChart: React.FC<InsolationChartProps> = ({ currentOrbitalAngleRad }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const currentLsDeg = useMemo(() => getSolarLongitudeDeg(currentOrbitalAngleRad), [currentOrbitalAngleRad]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, CHART_CANVAS_WIDTH, CHART_CANVAS_HEIGHT);
    ctx.fillStyle = 'rgba(0,0,0,0.35)'; 
    ctx.fillRect(0, 0, CHART_CANVAS_WIDTH, CHART_CANVAS_HEIGHT);
    
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    const LsRad = currentLsDeg * Math.PI / 180;
    const delta = Math.asin(Math.sin(OBLIQ_DEG * Math.PI / 180) * Math.sin(LsRad)); 
    const daylightHours = calculateDaylightHours(45, delta * 180 / Math.PI);

    ctx.save(); 
    ctx.font = 'bold 18px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillStyle = '#fff'; 
    ctx.fillText(`北纬45°白昼时长: ${daylightHours.toFixed(2)} 小时`, CHART_CANVAS_WIDTH / 2, 32);
    ctx.restore();

    setupTrackGeometry(CHART_CANVAS_WIDTH, CHART_CANVAS_HEIGHT, AXIS_MARGIN, TRACK_VISUAL_HEIGHT);

    ctx.save(); 
    ctx.translate(0, 25); 

    // --- Draw Axes ---
    ctx.strokeStyle = '#ffffff'; // Corrected color
    ctx.lineWidth = 2; // Corrected line width
    ctx.beginPath();
    ctx.moveTo(AXIS_MARGIN, AXIS_MARGIN);
    ctx.lineTo(AXIS_MARGIN, (CHART_CANVAS_HEIGHT - 25) - AXIS_MARGIN); 
    ctx.lineTo(CHART_CANVAS_WIDTH - AXIS_MARGIN, (CHART_CANVAS_HEIGHT - 25) - AXIS_MARGIN);
    ctx.stroke();

    ctx.fillStyle = '#fff'; 
    ctx.font = 'bold 12px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('太阳赤纬 (°)', CHART_CANVAS_WIDTH / 2, (CHART_CANVAS_HEIGHT - 25) - AXIS_MARGIN + 20);

    ctx.textBaseline = 'top';
    ctx.textAlign = 'left';
    ctx.fillText('-23.44°', AXIS_MARGIN + 10, (CHART_CANVAS_HEIGHT - 25) - AXIS_MARGIN + 2);
    ctx.textAlign = 'right';
    ctx.fillText('+23.44°', CHART_CANVAS_WIDTH - AXIS_MARGIN - 30, (CHART_CANVAS_HEIGHT - 25) - AXIS_MARGIN + 2);
    
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.fillText('100%', AXIS_MARGIN - 8, AXIS_MARGIN + 5); 
    ctx.fillText('0%', AXIS_MARGIN - 8, CHART_CANVAS_HEIGHT - AXIS_MARGIN); 
    
    ctx.save();
    // Corrected Y translation for Y-axis title
    ctx.translate(12, (CHART_CANVAS_HEIGHT / 2) + 150 - 25); 
    ctx.rotate(-Math.PI / 2);
    ctx.textAlign = 'center';
    ctx.font = 'bold 12px sans-serif';
    ctx.fillText('北半球阳照面积 (%)', 0, 0);
    ctx.restore();

    let trackEnvMaxY_canvas = -Infinity, trackEnvMinY_canvas = Infinity; 
    const N_sample = 300;
    for (let i = 0; i < N_sample; i++) {
        const t = i / (N_sample - 1);
        const localPt = getTrackLocalPositionTestEarth2(t, TRACK_GEOMETRY_PARAMS);
        const pt = transformTrackLocalToCanvasTestEarth2(localPt.x, localPt.y, TRACK_TRANSFORM_PARAMS);
        if (pt.y > trackEnvMaxY_canvas) { trackEnvMaxY_canvas = pt.y; } 
        if (pt.y < trackEnvMinY_canvas) { trackEnvMinY_canvas = pt.y; } 
    }

    ctx.save();
    ctx.globalAlpha = 0.45;
    ctx.fillStyle = '#ff0';
    ctx.fillRect(AXIS_MARGIN - 2, trackEnvMinY_canvas - 25, 4, (trackEnvMaxY_canvas - 25) - (trackEnvMinY_canvas - 25));
    ctx.restore();

    ctx.save();
    ctx.strokeStyle = '#ff0';
    ctx.lineWidth = 1.5;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(AXIS_MARGIN, trackEnvMinY_canvas - 25); // Visually higher point on track labeled 37%
    ctx.lineTo(CHART_CANVAS_WIDTH - AXIS_MARGIN, trackEnvMinY_canvas - 25);
    ctx.moveTo(AXIS_MARGIN, trackEnvMaxY_canvas - 25); // Visually lower point on track labeled 63%
    ctx.lineTo(CHART_CANVAS_WIDTH - AXIS_MARGIN, trackEnvMaxY_canvas - 25);
    ctx.stroke();
    ctx.setLineDash([]);
    
    ctx.fillStyle = '#ff0';
    ctx.font = 'bold 12px sans-serif';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.fillText('37%', AXIS_MARGIN - 10, trackEnvMinY_canvas - 25); 
    ctx.fillText('63%', AXIS_MARGIN - 10, trackEnvMaxY_canvas - 25); 
    ctx.restore();


    // --- Draw Track Outline ---
    ctx.save();
    ctx.translate(TRACK_TRANSFORM_PARAMS.CX, TRACK_TRANSFORM_PARAMS.CY - 25); 
    ctx.rotate(TRACK_TRANSFORM_PARAMS.ALPHA);
    const xs = -TRACK_GEOMETRY_PARAMS.lenStraight / 2;
    const xe = TRACK_GEOMETRY_PARAMS.lenStraight / 2;
    const r_track = TRACK_GEOMETRY_PARAMS.r;
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1.5;
    ctx.beginPath();
    ctx.moveTo(xs, -r_track);
    ctx.lineTo(xe, -r_track);
    ctx.arc(xe, 0, r_track, -Math.PI / 2, Math.PI / 2, false);
    ctx.lineTo(xs, r_track);
    ctx.arc(xs, 0, r_track, Math.PI / 2, 3 * Math.PI / 2, false);
    ctx.stroke();
    ctx.restore();

    // --- Draw Insolation Curve (blue line) ---
    ctx.save();
    ctx.translate(TRACK_TRANSFORM_PARAMS.CX, TRACK_TRANSFORM_PARAMS.CY - 25); 
    ctx.rotate(TRACK_TRANSFORM_PARAMS.ALPHA);
    ctx.beginPath();
    ctx.strokeStyle = '#44f';
    ctx.lineWidth = 2;
    const Ls0_ref = 0; 
    const insolationFraction = (LsDeg_param: number) => {
        const obliqRad = OBLIQ_DEG * Math.PI / 180; 
        const delta_param = Math.asin(Math.sin(obliqRad) * Math.sin(LsDeg_param * Math.PI / 180));
        return 50 + 50 * Math.sin(delta_param); 
    };
    for (let i = 0; i <= 360; i++) {
      const Ls = (Ls0_ref + i) % 360;
      const ratio = mapSolarLongitudeToTrackRatioTestEarth2(Ls); // Use corrected mapping name
      const base = getTrackLocalPositionTestEarth2(ratio, TRACK_GEOMETRY_PARAMS);
      const offset = (insolationFraction(Ls) - 50) * 0.05; 
      const px = base.x;
      const py = base.y - offset; 
      if (i === 0) ctx.moveTo(px, py); else ctx.lineTo(px, py);
    }
    ctx.stroke();
    ctx.restore();

    // --- Draw Solar Term Labels ---
    ctx.font = '10px sans-serif';
    ctx.fillStyle = '#fff'; 
    ctx.textBaseline = 'middle';
    const upperTerms: { name: string; x: number; y: number; Ls: number }[] = [];
    const lowerTerms: { name: string; x: number; y: number; Ls: number }[] = [];

    TERM_NAMES.forEach((name, i) => {
      // Calculate correct solar longitude for each term
      // TERM_NAMES[0] is 立春 which corresponds to Ls=315°
      // Each subsequent term is 15° later
      const termLs = (315 + i * 15) % 360; 
      const ratio = mapSolarLongitudeToTrackRatioTestEarth2(termLs); // Use corrected mapping name
      const localPos = getTrackLocalPositionTestEarth2(ratio, TRACK_GEOMETRY_PARAMS);
      const pos = transformTrackLocalToCanvasTestEarth2(localPos.x, localPos.y, TRACK_TRANSFORM_PARAMS);
      const isUp = localPos.y < 0;
      (isUp ? upperTerms : lowerTerms).push({ name, x: pos.x, y: pos.y - 25, Ls: termLs }); 
    });

    upperTerms.sort((a, b) => a.x - b.x);
    lowerTerms.sort((a, b) => a.x - b.x);

    const GAP_UP = 22, GAP_LOW = 26;
    const plotTermLabel = (o: { name: string; x: number; y: number; Ls: number }, ly_abs: number, isUp: boolean) => {
      const ly_translated = ly_abs; 

      ctx.strokeStyle = '#777';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(o.x, o.y); 
      ctx.lineTo(o.x, o.y + (isUp ? -5 : 5)); 
      ctx.lineTo(o.x, ly_translated); 
      ctx.stroke();
      ctx.textAlign = 'center';
      ctx.fillStyle = '#fff'; 
      ctx.fillText(`${o.name}(${TERM_TO_MONTH[o.name as keyof typeof TERM_TO_MONTH]}月)`, o.x, ly_translated);
    };
    upperTerms.forEach((o, idx) => plotTermLabel(o, o.y - (20 + idx * GAP_UP), true));
    lowerTerms.forEach((o, idx) => plotTermLabel(o, o.y + (20 + idx * GAP_LOW), false));

    // --- Draw Current Solar Term Point (Red Dot) ---
    const currentTermRatio = mapSolarLongitudeToTrackRatioTestEarth2(currentLsDeg); // Use corrected mapping name
    const currentLocalPos = getTrackLocalPositionTestEarth2(currentTermRatio, TRACK_GEOMETRY_PARAMS);
    const currentCanvasPos_abs = transformTrackLocalToCanvasTestEarth2(currentLocalPos.x, currentLocalPos.y, TRACK_TRANSFORM_PARAMS);
    const currentCanvasPos_translated = { x: currentCanvasPos_abs.x, y: currentCanvasPos_abs.y - 25 };


    ctx.fillStyle = '#ff0'; 
    ctx.beginPath();
    ctx.arc(currentCanvasPos_translated.x, currentCanvasPos_translated.y, 4, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.font = 'bold 12px sans-serif';
    ctx.fillStyle = '#fff'; 
    ctx.textAlign = 'left';
    ctx.textBaseline = 'bottom';
    const currentTermIndex = getSolarTermIndexFromLs(currentLsDeg);
    const currentTermName = TERM_NAMES[currentTermIndex];
    ctx.fillText(
      `${currentTermName}(${TERM_TO_MONTH[currentTermName as keyof typeof TERM_TO_MONTH]}月)`,
      currentCanvasPos_translated.x + 8,
      currentCanvasPos_translated.y - 6
    );
    
    ctx.restore(); 

  }, [currentLsDeg]);

  return (
    <canvas
      ref={canvasRef}
      width={CHART_CANVAS_WIDTH}
      height={CHART_CANVAS_HEIGHT}
      aria-label="Northern Hemisphere Insolation and Solar Terms Chart"
      className="rounded"
    />
  );
};

export default InsolationChart;
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI文件夹上传工具
提供图形界面，支持拖拽文件夹上传
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from pathlib import Path
from desktop_folder_uploader import FolderUploader

class FolderUploaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件夹批量上传工具 - 技术知识管理系统")
        self.root.geometry("600x500")
        
        # 创建上传器实例
        self.uploader = FolderUploader()
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        self.setup_ui()
        
        # 支持拖拽
        self.root.drop_target_register('DND_Files')
        self.root.dnd_bind('<<Drop>>', self.on_drop)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="📁 文件夹批量上传工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 服务器地址设置
        ttk.Label(main_frame, text="服务器地址:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.server_var = tk.StringVar(value="http://localhost:8081")
        server_entry = ttk.Entry(main_frame, textvariable=self.server_var, width=40)
        server_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 文件夹选择
        ttk.Label(main_frame, text="选择文件夹:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        folder_frame = ttk.Frame(main_frame)
        folder_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        self.folder_var = tk.StringVar()
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, width=30)
        folder_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        browse_btn = ttk.Button(folder_frame, text="浏览", command=self.browse_folder)
        browse_btn.grid(row=0, column=1, padx=(5, 0))
        
        # 拖拽区域
        drop_frame = ttk.LabelFrame(main_frame, text="拖拽上传区域", padding="20")
        drop_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=20)
        
        drop_label = ttk.Label(drop_frame, 
                              text="🎯 将文件夹拖拽到这里\n或使用上方的浏览按钮选择文件夹",
                              font=('Arial', 12),
                              foreground='blue',
                              background='lightblue',
                              relief='dashed',
                              borderwidth=2,
                              padding="40")
        drop_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 选项
        options_frame = ttk.LabelFrame(main_frame, text="上传选项", padding="10")
        options_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        self.create_knowledge_var = tk.BooleanVar(value=True)
        knowledge_check = ttk.Checkbutton(options_frame, 
                                         text="为文件夹创建知识条目",
                                         variable=self.create_knowledge_var)
        knowledge_check.grid(row=0, column=0, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        self.upload_btn = ttk.Button(button_frame, text="🚀 开始上传", 
                                    command=self.start_upload, style='Accent.TButton')
        self.upload_btn.grid(row=0, column=0, padx=5)
        
        clear_btn = ttk.Button(button_frame, text="🗑️ 清空", command=self.clear_form)
        clear_btn.grid(row=0, column=1, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # 状态显示
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                foreground='green')
        status_label.grid(row=7, column=0, columnspan=2, pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="上传日志", padding="10")
        log_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # 创建滚动文本框
        self.log_text = tk.Text(log_frame, height=8, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(8, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        folder_frame.columnconfigure(0, weight=1)
        drop_frame.columnconfigure(0, weight=1)
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def browse_folder(self):
        """浏览文件夹"""
        folder_path = filedialog.askdirectory(title="选择要上传的文件夹")
        if folder_path:
            self.folder_var.set(folder_path)
            self.log(f"📁 选择文件夹: {folder_path}")
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = self.root.tk.splitlist(event.data)
        if files:
            folder_path = files[0]
            if os.path.isdir(folder_path):
                self.folder_var.set(folder_path)
                self.log(f"📁 拖拽文件夹: {folder_path}")
            else:
                messagebox.showwarning("警告", "请拖拽文件夹，不是文件")
    
    def log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_form(self):
        """清空表单"""
        self.folder_var.set("")
        self.progress_var.set(0)
        self.status_var.set("准备就绪")
        self.log_text.delete(1.0, tk.END)
        self.upload_btn.config(state='normal')
    
    def start_upload(self):
        """开始上传"""
        folder_path = self.folder_var.get().strip()
        
        if not folder_path:
            messagebox.showerror("错误", "请选择要上传的文件夹")
            return
        
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            messagebox.showerror("错误", "文件夹不存在或不是有效的文件夹")
            return
        
        # 禁用上传按钮
        self.upload_btn.config(state='disabled')
        self.status_var.set("正在上传...")
        self.progress_var.set(0)
        
        # 在新线程中执行上传
        upload_thread = threading.Thread(target=self.upload_worker, args=(folder_path,))
        upload_thread.daemon = True
        upload_thread.start()
    
    def upload_worker(self, folder_path):
        """上传工作线程"""
        try:
            # 更新上传器配置
            self.uploader.server_url = self.server_var.get().rstrip('/')
            
            self.log(f"🚀 开始上传文件夹: {os.path.basename(folder_path)}")
            
            # 扫描文件
            files_to_upload = self.uploader.scan_folder(folder_path)
            
            if not files_to_upload:
                self.log("❌ 没有找到支持的文件")
                self.status_var.set("没有找到支持的文件")
                self.upload_btn.config(state='normal')
                return
            
            total_files = len(files_to_upload)
            self.log(f"📊 找到 {total_files} 个支持的文件")
            
            # 创建知识条目
            knowledge_id = None
            if self.create_knowledge_var.get():
                knowledge_id = self.uploader.create_knowledge_entry(
                    os.path.basename(folder_path), folder_path, total_files
                )
                if knowledge_id:
                    self.log(f"✅ 创建知识条目成功，ID: {knowledge_id}")
            
            # 上传文件
            success_count = 0
            error_count = 0
            
            for i, file_info in enumerate(files_to_upload, 1):
                # 更新进度
                progress = (i / total_files) * 100
                self.progress_var.set(progress)
                self.status_var.set(f"上传中... ({i}/{total_files})")
                
                self.log(f"[{i}/{total_files}] {file_info['relative_path']}")
                
                success, result = self.uploader.upload_file_direct(file_info, knowledge_id)
                
                if success:
                    success_count += 1
                else:
                    error_count += 1
                    self.log(f"❌ 上传失败: {result}")
            
            # 完成
            self.progress_var.set(100)
            self.status_var.set(f"上传完成! 成功: {success_count}, 失败: {error_count}")
            
            self.log(f"\n🎉 上传完成!")
            self.log(f"✅ 成功: {success_count} 个文件")
            if error_count > 0:
                self.log(f"❌ 失败: {error_count} 个文件")
            
            if knowledge_id:
                self.log(f"🔗 知识条目ID: {knowledge_id}")
                self.log(f"🌐 查看地址: {self.uploader.server_url}/knowledge/{knowledge_id}")
            
            # 显示完成对话框
            if error_count == 0:
                result = messagebox.showinfo("上传完成", 
                    f"成功上传 {success_count} 个文件!\n\n" +
                    (f"知识条目ID: {knowledge_id}\n" if knowledge_id else "") +
                    "是否打开浏览器查看?")
                
                if knowledge_id and messagebox.askyesno("打开浏览器", "是否在浏览器中查看上传的内容?"):
                    import webbrowser
                    webbrowser.open(f"{self.uploader.server_url}/knowledge/{knowledge_id}")
            else:
                messagebox.showwarning("上传完成", 
                    f"上传完成，但有 {error_count} 个文件失败。\n请查看日志了解详情。")
        
        except Exception as e:
            self.log(f"❌ 上传过程中发生错误: {e}")
            self.status_var.set("上传失败")
            messagebox.showerror("错误", f"上传过程中发生错误:\n{e}")
        
        finally:
            # 重新启用上传按钮
            self.upload_btn.config(state='normal')


def main():
    """主函数"""
    root = tk.Tk()
    
    # 尝试启用拖拽支持
    try:
        from tkinterdnd2 import DND_FILES, TkinterDnD
        root = TkinterDnD.Tk()
    except ImportError:
        print("提示: 安装 tkinterdnd2 可以支持拖拽功能")
        print("pip install tkinterdnd2")
    
    app = FolderUploaderGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()

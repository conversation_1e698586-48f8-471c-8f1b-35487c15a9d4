export const OBLIQ_DEG = 23.44; // Earth's axial tilt in degrees
export const OBLIQ_RAD = OBLIQ_DEG * Math.PI / 180;

export const AXIS_MARGIN = 45; // Margin for axes and labels, as per testearth2.html M

// Solar Declination Range
export const X_DECL_MIN = -OBLIQ_DEG;
export const X_DECL_MAX = OBLIQ_DEG;

// Insolation Percentage Range on Y-axis
export const Y_INSOL_MIN = 0;
export const Y_INSOL_MAX = 100;

// Corrected:
// INSOL_TRACK_MIN_PERCENT (37%) corresponds to Winter Solstice (min northern hemisphere insolation).
// This should map to p1.y (visually higher point on the track in testearth2.html, smaller canvas Y value).
// INSOL_TRACK_MAX_PERCENT (63%) corresponds to Summer Solstice (max northern hemisphere insolation).
// This should map to p2.y (visually lower point on the track in testearth2.html, larger canvas Y value).
// The mapInsolationToCanvasY function maps higher percentages to smaller Y values (higher on screen).
// Therefore, to make p1.y (top-left anchor) represent Winter Solstice (37%, visually highest point of track in testearth2.html),
// p1.y should use mapInsolationToCanvasY(37, ...).
// And p2.y (bottom-right anchor) represent Summer Solstice (63%, visually lowest point of track in testearth2.html),
// p2.y should use mapInsolationToCanvasY(63, ...).
export const INSOL_TRACK_MIN_PERCENT = 37; // Winter Solstice - min N.H. insolation (e.g., for p1.y)
export const INSOL_TRACK_MAX_PERCENT = 63; // Summer Solstice - max N.H. insolation (e.g., for p2.y)


// Corrected to match testearth2.html order: starting with 立春 (Ls=315°)
// This ensures proper alignment with the track geometry where Winter Solstice is at ratio 0
export const TERM_NAMES: string[] = [
  '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
  '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
  '立秋', '处暑', '白露', '秋分', '寒露', '霜降',
  '立冬', '小雪', '大雪', '冬至', '小寒', '大寒'
];

export const TERM_TO_MONTH: { [key: string]: number } = {
  '小寒': 1, '大寒': 1, '立春': 2, '雨水': 2, '惊蛰': 3, '春分': 3,
  '清明': 4, '谷雨': 4, '立夏': 5, '小满': 5, '芒种': 6, '夏至': 6,
  '小暑': 7, '大暑': 7, '立秋': 8, '处暑': 8, '白露': 9, '秋分': 9,
  '寒露': 10, '霜降': 10, '立冬': 11, '小雪': 11, '大雪': 12, '冬至': 12
};

// --- Ported from testearth2.html ---
export const TRACK_GEOMETRY_PARAMS = {
  r: 0,
  lenStraight: 0,
  s1: 0, s2: 0, s3: 0, s4: 0, per: 0,
};

export const TRACK_TRANSFORM_PARAMS = {
  p1: { x: 0, y: 0 },
  p2: { x: 0, y: 0 },
  CX: 0, CY: 0, ALPHA: 0,
};

export function setupTrackGeometry(
  chartWidth: number, // canvas width
  chartHeight: number, // canvas height
  margin: number, // M
  trackVisualHeight: number // testearth2.html trackH = 15
) {
  TRACK_GEOMETRY_PARAMS.r = trackVisualHeight / 2;
  TRACK_GEOMETRY_PARAMS.lenStraight = chartWidth - 2 * margin - 60; // 60 is a fixed value from testearth2.html
  TRACK_GEOMETRY_PARAMS.s1 = TRACK_GEOMETRY_PARAMS.lenStraight;
  TRACK_GEOMETRY_PARAMS.s2 = Math.PI * TRACK_GEOMETRY_PARAMS.r;
  TRACK_GEOMETRY_PARAMS.s3 = TRACK_GEOMETRY_PARAMS.lenStraight;
  TRACK_GEOMETRY_PARAMS.s4 = Math.PI * TRACK_GEOMETRY_PARAMS.r;
  TRACK_GEOMETRY_PARAMS.per = TRACK_GEOMETRY_PARAMS.s1 + TRACK_GEOMETRY_PARAMS.s2 + TRACK_GEOMETRY_PARAMS.s3 + TRACK_GEOMETRY_PARAMS.s4;
  
  // p1.y is the top-left anchor for the track definition.
  // To match testearth2.html visual (Winter Solstice / 37% insolation at visually higher track point):
  // p1.y should use INSOL_TRACK_MIN_PERCENT (37)
  // p2.y is the bottom-right anchor.
  // To match testearth2.html visual (Summer Solstice / 63% insolation at visually lower track point):
  // p2.y should use INSOL_TRACK_MAX_PERCENT (63)
  TRACK_TRANSFORM_PARAMS.p1 = {
    x: mapSolarDeclinationToCanvasX(X_DECL_MIN, chartWidth, margin, X_DECL_MIN, X_DECL_MAX),
    y: mapInsolationToCanvasY(INSOL_TRACK_MIN_PERCENT, chartHeight, margin, Y_INSOL_MIN, Y_INSOL_MAX) 
  };
  TRACK_TRANSFORM_PARAMS.p2 = {
    x: mapSolarDeclinationToCanvasX(X_DECL_MAX, chartWidth, margin, X_DECL_MIN, X_DECL_MAX),
    y: mapInsolationToCanvasY(INSOL_TRACK_MAX_PERCENT, chartHeight, margin, Y_INSOL_MIN, Y_INSOL_MAX) 
  };

  TRACK_TRANSFORM_PARAMS.CX = (TRACK_TRANSFORM_PARAMS.p1.x + TRACK_TRANSFORM_PARAMS.p2.x) / 2;
  TRACK_TRANSFORM_PARAMS.CY = (TRACK_TRANSFORM_PARAMS.p1.y + TRACK_TRANSFORM_PARAMS.p2.y) / 2;
  TRACK_TRANSFORM_PARAMS.ALPHA = Math.atan2(
    TRACK_TRANSFORM_PARAMS.p2.y - TRACK_TRANSFORM_PARAMS.p1.y,
    TRACK_TRANSFORM_PARAMS.p2.x - TRACK_TRANSFORM_PARAMS.p1.x
  );
}


export function getTrackLocalPositionTestEarth2(ratioAlongPerimeter: number, params: typeof TRACK_GEOMETRY_PARAMS): { x: number; y: number } {
  let d = (ratioAlongPerimeter * params.per) % params.per;
  if (d < 0) d += params.per;

  const xs = -params.lenStraight / 2;
  const xe = params.lenStraight / 2;
  const r0 = params.r;

  if (d <= params.s1) return { x: xs + d, y: -r0 }; // Top straight (local Y is negative)
  d -= params.s1;
  if (d <= params.s2) { // Right curve
    const dd = d;
    const a = -Math.PI / 2 + dd / r0;
    return { x: xe + r0 * Math.cos(a), y: r0 * Math.sin(a) };
  }
  d -= params.s2;
  if (d <= params.s3) { // Bottom straight
    const dd = d;
    return { x: xe - dd, y: r0 }; // Local Y is positive
  }
  d -= params.s3;
  // Left curve
  const dd = d;
  const a = Math.PI / 2 + dd / r0;
  return { x: xs + r0 * Math.cos(a), y: r0 * Math.sin(a) };
}

export function transformTrackLocalToCanvasTestEarth2(px: number, py: number, transformParams: typeof TRACK_TRANSFORM_PARAMS): { x: number; y: number } {
  const c = Math.cos(transformParams.ALPHA);
  const s = Math.sin(transformParams.ALPHA);
  return {
    x: transformParams.CX + px * c - py * s,
    y: transformParams.CY + px * s + py * c
  };
}

// Corrected mapping function to match testearth2.html behavior.
// Maps Ls=315° (Winter Solstice) to ratio 0, following the track layout.
// This ensures proper distribution of solar terms around the track.
export function mapSolarLongitudeToTrackRatioTestEarth2(LsDeg: number): number {
  return ((LsDeg - 315 + 360) % 360) / 360;
}
// --- End of ported functions ---


export function getSolarLongitudeDeg(orbitalAngleRad: number): number {
  let LsDeg = (orbitalAngleRad * 180 / Math.PI) % 360;
  if (LsDeg < 0) LsDeg += 360;
  return LsDeg;
}

export function getSolarDeclinationDeg(LsDeg: number): number {
  const LsRad = LsDeg * Math.PI / 180;
  return Math.asin(Math.sin(OBLIQ_RAD) * Math.sin(LsRad)) * 180 / Math.PI;
}

// Calculates true insolation percentage (0-100 scale based on declination)
export function getInsolationPercentage(solarDeclinationDeg: number): number {
  const deltaRad = solarDeclinationDeg * Math.PI / 180;
  // This formula maps declination range [-OBLIQ_DEG, +OBLIQ_DEG] to insolation values.
  // Min insolation: 50 + 50 * sin(-OBLIQ_RAD) approx 50 - 19.88 = 30.12%
  // Max insolation: 50 + 50 * sin(+OBLIQ_RAD) approx 50 + 19.88 = 69.88%
  return 50 + 50 * Math.sin(deltaRad);
}


export function calculateDaylightHours(latitudeDeg: number, solarDeclinationDeg: number): number {
  const latRad = latitudeDeg * Math.PI / 180;
  const declRad = solarDeclinationDeg * Math.PI / 180;

  const cosH0 = -Math.tan(latRad) * Math.tan(declRad);

  if (cosH0 >= 1) return 24; 
  if (cosH0 <= -1) return 0;  

  const H0 = Math.acos(cosH0); 
  return (2 * H0 * 12 / Math.PI); 
}

export function mapSolarDeclinationToCanvasX(
  declDeg: number,
  canvasWidth: number,
  margin: number,
  minDecl: number, // Should be X_DECL_MIN
  maxDecl: number  // Should be X_DECL_MAX
): number {
  const chartWidth = canvasWidth - 2 * margin;
  return margin + ((declDeg - minDecl) / (maxDecl - minDecl)) * chartWidth;
}

export function mapInsolationToCanvasY(
  insolPercent: number,
  canvasHeight: number, // Full canvas height
  margin: number, // M
  minInsol: number, // Y_INSOL_MIN (0)
  maxInsol: number  // Y_INSOL_MAX (100)
): number {
  // This function maps percentage to canvas Y.
  // Higher percentage means smaller Y value (higher on screen).
  // chartDrawHeight in testearth2.html's mapY is H-2*M
  const chartDrawableHeight = canvasHeight - 2 * margin;
  return margin + (1 - (insolPercent - minInsol) / (maxInsol - minInsol)) * chartDrawableHeight;
}

export function getSolarTermIndexFromLs(LsDeg: number): number {
  // Since TERM_NAMES[0] is 立春 (Ls = 315°), we need to adjust the calculation
  // Convert Ls to term index: subtract 315 and divide by 15
  return Math.round(((LsDeg - 315 + 360) % 360) / 15) % 24;
}


import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Season, WebGLProgramInfo } from '../types';
import { initShaderProgram, createTransformMatrix, createCircleVertices } from '../utils/webglUtils';

const VS_SOURCE = `
  attribute vec2 aVertexPosition;
  uniform mat3 uTransformMatrix; 

  void main() {
    gl_Position = vec4((uTransformMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
  }
`;

const FS_SOURCE = `
  precision mediump float;
  uniform vec4 uColor;

  void main() {
    gl_FragColor = uColor;
  }
`;

const ORBIT_RX_BASE = 150; // Adjusted base for smaller display
const ORBIT_RY_BASE = 80;  // Adjusted base
const SUN_RADIUS_WORLD_BASE = 15;
const EARTH_RADIUS_WORLD_BASE = 7;
const CANVAS_WIDTH_BASE = 400; // Adjusted for smaller column
const CANVAS_HEIGHT_BASE = 280; // Adjusted
const ORBIT_PERIOD_MS = 20000; 

interface SolarSystemProps {
  onAngleUpdate: (angleRad: number) => void;
}

const SolarSystem: React.FC<SolarSystemProps> = ({ onAngleUpdate }) => {
  const webglCanvasRef = useRef<HTMLCanvasElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  
  const glRef = useRef<WebGLRenderingContext | null>(null);
  const programInfoRef = useRef<WebGLProgramInfo | null>(null);
  const sunBufferRef = useRef<WebGLBuffer | null>(null);
  const earthBufferRef = useRef<WebGLBuffer | null>(null);
  const circleVerticesRef = useRef<Float32Array | null>(null);
  
  const animationFrameIdRef = useRef<number | null>(null);
  const lastTimestampRef = useRef<number>(0);
  const totalElapsedTimeRef = useRef<number>(0); 

  const [currentSeason, setCurrentSeason] = useState<Season>(Season.Spring);
  
  // Adjust dimensions based on a scale factor if needed, or keep fixed
  const scaleFactor = 1.0; // Example: can be adjusted
  const ORBIT_RX = ORBIT_RX_BASE * scaleFactor;
  const ORBIT_RY = ORBIT_RY_BASE * scaleFactor;
  const SUN_RADIUS_WORLD = SUN_RADIUS_WORLD_BASE * scaleFactor;
  const EARTH_RADIUS_WORLD = EARTH_RADIUS_WORLD_BASE * scaleFactor;
  const CANVAS_WIDTH = CANVAS_WIDTH_BASE * scaleFactor;
  const CANVAS_HEIGHT = CANVAS_HEIGHT_BASE * scaleFactor;


  const getSeason = (angle: number): Season => {
    const twoPi = 2 * Math.PI;
    let normalizedAngle = angle % twoPi;
    if (normalizedAngle < 0) {
      normalizedAngle += twoPi;
    }
    
    // Standard astronomical seasons: Spring equinox at 0 rad, Summer solstice at PI/2, etc.
    if (normalizedAngle >= 0 && normalizedAngle < Math.PI / 2) {
      return Season.Spring; 
    } else if (normalizedAngle >= Math.PI / 2 && normalizedAngle < Math.PI) {
      return Season.Summer; 
    } else if (normalizedAngle >= Math.PI && normalizedAngle < (3 * Math.PI) / 2) {
      return Season.Autumn; 
    } else {
      return Season.Winter; 
    }
  };

  const drawScene = useCallback(() => {
    const gl = glRef.current;
    const programInfo = programInfoRef.current;
    if (!gl || !programInfo) return;

    gl.clearColor(1.0, 1.0, 1.0, 1.0); 
    gl.clear(gl.COLOR_BUFFER_BIT);
    
    const worldToClipScaleX = 2.0 / CANVAS_WIDTH;
    const worldToClipScaleY = 2.0 / CANVAS_HEIGHT;

    // --- Draw Sun ---
    if (sunBufferRef.current && circleVerticesRef.current) {
      gl.useProgram(programInfo.program);
      
      const sunScaleX = SUN_RADIUS_WORLD * worldToClipScaleX;
      const sunScaleY = SUN_RADIUS_WORLD * worldToClipScaleY;
      const sunTransformMatrix = createTransformMatrix(0, 0, sunScaleX, sunScaleY); 
      
      gl.uniformMatrix3fv(programInfo.uniformLocations.modelViewMatrix, false, sunTransformMatrix);
      gl.uniform4f(programInfo.uniformLocations.color, 0.0, 0.0, 0.0, 1.0); // Black

      gl.bindBuffer(gl.ARRAY_BUFFER, sunBufferRef.current);
      gl.vertexAttribPointer(programInfo.attribLocations.vertexPosition, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);
      gl.drawArrays(gl.TRIANGLE_FAN, 0, circleVerticesRef.current.length / 2);
    }

    // --- Draw Earth ---
    if (earthBufferRef.current && circleVerticesRef.current) {
      gl.useProgram(programInfo.program);

      const angle = (totalElapsedTimeRef.current / ORBIT_PERIOD_MS) * 2 * Math.PI;
      onAngleUpdate(angle); // Report current angle

      const earthWorldX = ORBIT_RX * Math.cos(angle);
      const earthWorldY = ORBIT_RY * Math.sin(angle);

      const earthTranslateClipX = earthWorldX * worldToClipScaleX;
      const earthTranslateClipY = earthWorldY * worldToClipScaleY;
      const earthScaleClipX = EARTH_RADIUS_WORLD * worldToClipScaleX;
      const earthScaleClipY = EARTH_RADIUS_WORLD * worldToClipScaleY;

      const earthTransformMatrix = createTransformMatrix(earthTranslateClipX, earthTranslateClipY, earthScaleClipX, earthScaleClipY);
      
      gl.uniformMatrix3fv(programInfo.uniformLocations.modelViewMatrix, false, earthTransformMatrix);
      gl.uniform4f(programInfo.uniformLocations.color, 0.0, 0.0, 0.0, 1.0); // Black

      gl.bindBuffer(gl.ARRAY_BUFFER, earthBufferRef.current);
      gl.vertexAttribPointer(programInfo.attribLocations.vertexPosition, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);
      gl.drawArrays(gl.TRIANGLE_FAN, 0, circleVerticesRef.current.length / 2);
      
      setCurrentSeason(getSeason(angle));
    }

  }, [CANVAS_WIDTH, CANVAS_HEIGHT, EARTH_RADIUS_WORLD, ORBIT_RX, ORBIT_RY, SUN_RADIUS_WORLD, onAngleUpdate]); 

  const animate = useCallback((timestamp: number) => {
    if (lastTimestampRef.current === 0) { 
      lastTimestampRef.current = timestamp;
    }
    const deltaTime = timestamp - lastTimestampRef.current;
    lastTimestampRef.current = timestamp;

    totalElapsedTimeRef.current += deltaTime;
    
    drawScene();
    animationFrameIdRef.current = requestAnimationFrame(animate);
  }, [drawScene]);


  useEffect(() => {
    const canvas = webglCanvasRef.current;
    if (!canvas) return;

    const gl = canvas.getContext('webgl', { antialias: true });
    if (!gl) {
      console.error("Unable to initialize WebGL for SolarSystem.");
      return;
    }
    glRef.current = gl;
    // Set viewport based on possibly scaled canvas dimensions
    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);


    const shaderProgram = initShaderProgram(gl, VS_SOURCE, FS_SOURCE);
    if (!shaderProgram) return;

    programInfoRef.current = {
      program: shaderProgram,
      attribLocations: {
        vertexPosition: gl.getAttribLocation(shaderProgram, 'aVertexPosition'),
      },
      uniformLocations: {
        modelViewMatrix: gl.getUniformLocation(shaderProgram, 'uTransformMatrix'), 
        color: gl.getUniformLocation(shaderProgram, 'uColor'),
        projectionMatrix: null, 
      },
    };
    
    const segments = 32;
    const unitCircleVerts = createCircleVertices(1.0, segments);
    circleVerticesRef.current = unitCircleVerts;

    sunBufferRef.current = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, sunBufferRef.current);
    gl.bufferData(gl.ARRAY_BUFFER, unitCircleVerts, gl.STATIC_DRAW);

    earthBufferRef.current = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, earthBufferRef.current);
    gl.bufferData(gl.ARRAY_BUFFER, unitCircleVerts, gl.STATIC_DRAW);
    
    // Reset animation state for robustness if component re-mounts or visibility changes
    lastTimestampRef.current = 0;
    // totalElapsedTimeRef.current can be preserved or reset based on desired behavior on re-focus.
    // For now, it continues from where it left off if app loses focus and regains.

    animationFrameIdRef.current = requestAnimationFrame(animate);

    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (animationFrameIdRef.current) {
          cancelAnimationFrame(animationFrameIdRef.current);
          animationFrameIdRef.current = null; 
        }
      } else {
        if (!animationFrameIdRef.current) { 
          lastTimestampRef.current = 0; // Reset timestamp to avoid large jump
          animationFrameIdRef.current = requestAnimationFrame(animate);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      // WebGL resource cleanup
      const currentGl = glRef.current;
      if (currentGl) {
          if (sunBufferRef.current) currentGl.deleteBuffer(sunBufferRef.current);
          if (earthBufferRef.current) currentGl.deleteBuffer(earthBufferRef.current);
          const currentProgramInfo = programInfoRef.current;
          if (currentProgramInfo?.program) currentGl.deleteProgram(currentProgramInfo.program);
      }
    };
  }, [animate]); // Removed CANVAS_WIDTH, CANVAS_HEIGHT from dependencies as they are derived now

  return (
    <div className="relative flex flex-col items-center" role="figure" aria-labelledby="solarsystem-caption">
      <div 
        id="solarsystem-caption"
        className="text-black text-3xl font-semibold mb-2" // Adjusted font size
        style={{ height: '40px', textAlign: 'center' }} 
        aria-live="polite"
      >
        {currentSeason}
      </div>
      <div style={{ width: CANVAS_WIDTH, height: CANVAS_HEIGHT, position: 'relative' }}>
        <canvas
          ref={webglCanvasRef}
          width={CANVAS_WIDTH}
          height={CANVAS_HEIGHT}
          className="absolute top-0 left-0 z-0 rounded" 
          aria-label="WebGL animation of Earth orbiting the Sun"
        />
        <svg
          ref={svgRef}
          width={CANVAS_WIDTH}
          height={CANVAS_HEIGHT}
          className="absolute top-0 left-0 z-10 rounded" 
          viewBox={`0 0 ${CANVAS_WIDTH} ${CANVAS_HEIGHT}`}
          aria-hidden="true"
        >
          <ellipse
            cx={CANVAS_WIDTH / 2}
            cy={CANVAS_HEIGHT / 2}
            rx={ORBIT_RX}
            ry={ORBIT_RY}
            fill="none"
            stroke="black"
            strokeWidth="1.5"
          />
        </svg>
      </div>
    </div>
  );
};

export default SolarSystem;
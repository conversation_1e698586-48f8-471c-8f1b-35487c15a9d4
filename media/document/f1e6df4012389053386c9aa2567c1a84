# 节气分布修复过程详解

## 问题描述

在太阳系动力学可视化项目中，24节气在椭圆跑道上的分布不正确。节气应该按照天文学定义正确分布，但实际显示时出现了位置错误。

## 问题分析

### 1. 参考实现分析

通过分析 `testearth2.html` 参考实现，发现正确的实现应该包含：

```javascript
// testearth2.html 中的关键函数
function LsToTrackRatio(LsDeg) { 
    return ((LsDeg - 315 + 360) % 360) / 360; 
}

// 节气顺序从立春开始
const termNames = [
    '立春','雨水','惊蛰','春分','清明','谷雨',
    '立夏','小满','芒种','夏至','小暑','大暑',
    '立秋','处暑','白露','秋分','寒露','霜降',
    '立冬','小雪','大雪','冬至','小寒','大寒'
];
```

### 2. 原始代码问题识别

原始代码中存在三个主要问题：

#### 问题1：错误的太阳黄经到跑道位置映射
```typescript
// 错误的实现
export function mapSolarLongitudeToTrackRatioTestEarth2(LsDeg: number): number {
  return (LsDeg % 360) / 360;
}
```

这个实现让春分（Ls=0°）映射到跑道起点，但正确的应该是冬至（Ls=315°）映射到起点。

#### 问题2：错误的节气顺序
```typescript
// 错误的顺序（从春分开始）
export const TERM_NAMES: string[] = [
  '春分', '清明', '谷雨', '立夏', '小满', '芒种',
  '夏至', '小暑', '大暑', '立秋', '处暑', '白露',
  '秋分', '寒露', '霜降', '立冬', '小雪', '大雪',
  '冬至', '小寒', '大寒', '立春', '雨水', '惊蛰'
];
```

#### 问题3：错误的节气太阳黄经计算
```typescript
// 错误的计算（使用Ls0_ref = 0）
const termLs = (Ls0_ref + i * 15) % 360;
```

## 修复过程

### 步骤1：修正太阳黄经到跑道位置的映射

```typescript
// 修复后的实现
export function mapSolarLongitudeToTrackRatioTestEarth2(LsDeg: number): number {
  return ((LsDeg - 315 + 360) % 360) / 360;
}
```

**修复说明**：
- 减去315度，使得冬至（Ls=315°）映射到ratio=0（跑道起点）
- 加360度再取模，确保负数情况下的正确处理
- 这样春分（Ls=0°）映射到ratio=0.125，夏至（Ls=90°）映射到ratio=0.375

### 步骤2：修正节气顺序

```typescript
// 修复后的顺序（从立春开始，符合天文学定义）
export const TERM_NAMES: string[] = [
  '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
  '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
  '立秋', '处暑', '白露', '秋分', '寒露', '霜降',
  '立冬', '小雪', '大雪', '冬至', '小寒', '大寒'
];

// 同时更新月份映射
export const TERM_TO_MONTH: { [key: string]: number } = {
  '小寒': 1, '大寒': 1, '立春': 2, '雨水': 2, '惊蛰': 3, '春分': 3,
  '清明': 4, '谷雨': 4, '立夏': 5, '小满': 5, '芒种': 6, '夏至': 6,
  '小暑': 7, '大暑': 7, '立秋': 8, '处暑': 8, '白露': 9, '秋分': 9,
  '寒露': 10, '霜降': 10, '立冬': 11, '小雪': 11, '大雪': 12, '冬至': 12
};
```

### 步骤3：修正节气太阳黄经计算

```typescript
// 在 InsolationChart.tsx 中修复节气绘制
TERM_NAMES.forEach((name, i) => {
  // 正确的太阳黄经计算：立春=315°, 雨水=330°, 惊蛰=345°, 春分=0°, ...
  const termLs = (315 + i * 15) % 360; 
  const ratio = mapSolarLongitudeToTrackRatioTestEarth2(termLs);
  // ...后续处理
});
```

### 步骤4：修正节气索引计算函数

```typescript
export function getSolarTermIndexFromLs(LsDeg: number): number {
  // 由于TERM_NAMES[0]现在是立春（Ls=315°），需要调整计算
  return Math.round(((LsDeg - 315 + 360) % 360) / 15) % 24;
}
```

## 天文学知识背景

### 节气与太阳黄经的对应关系

| 节气 | 太阳黄经 | 序号 | 备注 |
|------|----------|------|------|
| 立春 | 315° | 0 | 跑道起点附近 |
| 雨水 | 330° | 1 | |
| 惊蛰 | 345° | 2 | |
| 春分 | 0° | 3 | 春季开始 |
| ... | ... | ... | ... |
| 冬至 | 270° | 21 | 北半球冬季开始 |
| 小寒 | 285° | 22 | |
| 大寒 | 300° | 23 | |

### 跑道几何说明

椭圆跑道的设计：
- 跑道起点（ratio=0）对应冬至附近
- 跑道具有上下两段直线和左右两个半圆
- 节气按照时间顺序（每15°太阳黄经）均匀分布

## 验证结果

修复后的效果：
1. ✅ 冬至正确位于跑道起点位置
2. ✅ 24个节气按照正确的天文顺序分布
3. ✅ 节气名称与月份的对应关系正确
4. ✅ 当前太阳位置的节气显示正确
5. ✅ 代码逻辑与参考实现testearth2.html保持一致

## 关键学习点

1. **天文坐标系统的重要性**：必须准确理解太阳黄经的定义和节气的天文学基础
2. **参考实现的价值**：仔细分析参考代码有助于理解正确的实现逻辑
3. **系统性修复**：多个相关函数需要协调修改，确保整体逻辑一致
4. **测试验证**：修复后需要验证各种边界情况（如年底年初的节气转换）

## 文件修改清单

- `utils/insolationChartUtils.ts`：修复核心映射函数和节气定义
- `components/InsolationChart.tsx`：修复节气绘制逻辑
- 清理未使用的导入，解决TypeScript类型错误

## 附加修复：地球渲染问题

在修复节气分布的过程中，还发现了地球Three.js组件的渲染问题，主要是纹理加载可能失败导致的显示问题。

### 地球渲染问题解决方案

1. **添加纹理加载错误处理**：
```typescript
// 创建基础材质作为回退
const fallbackMaterial = new THREE.MeshLambertMaterial({
  color: 0x4488cc, // 地球蓝色
  wireframe: false
});

// 异步加载纹理，失败时保持使用回退材质
textureLoader.load(
  EARTH_DAY_MAP_URL,
  (dayTexture) => {
    // 成功加载后更新材质
    const texturedMaterial = new THREE.MeshStandardMaterial({
      map: dayTexture,
      metalness: 0.1,
      roughness: 0.8,
    });
    earthMesh.material = texturedMaterial;
  },
  undefined,
  (error) => {
    console.error('地球纹理加载失败，使用回退材质:', error);
  }
);
```

2. **优化性能设置**：
```typescript
// 限制像素比例以提高性能
renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
```

3. **添加调试信息**：
```typescript
console.log('设置地球场景，尺寸:', width, height);
console.log('地球纹理加载成功');
```

这样即使网络纹理加载失败，地球也会显示为蓝色球体，保证基本功能正常。

此修复过程展示了如何系统性地分析和解决复杂的天文学可视化问题，强调了理解领域知识的重要性。
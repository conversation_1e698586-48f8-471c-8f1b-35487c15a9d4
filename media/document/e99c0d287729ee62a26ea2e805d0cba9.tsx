import React, { useRef, useEffect, useCallback } from 'react';
import * as THREE from 'three';

const RotatingEarthDebug: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);

  const sceneRef = useRef(new THREE.Scene());
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const earthMeshRef = useRef<THREE.Mesh | null>(null);

  const setupScene = useCallback((container: HTMLDivElement) => {
    const width = container.clientWidth;
    const height = container.clientHeight;

    console.log('Setting up scene with dimensions:', width, height);

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearColor(0x222222, 1); // Dark background for debugging
    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Camera
    const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    camera.position.set(0, 0, 3.5);
    cameraRef.current = camera;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    sceneRef.current.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
    directionalLight.position.set(5, 3, 5);
    sceneRef.current.add(directionalLight);

    // Earth with simple material first
    const geometry = new THREE.SphereGeometry(1, 32, 32);
    
    // Simple material for debugging
    const material = new THREE.MeshLambertMaterial({
      color: 0x4444ff,
      wireframe: false
    });

    const earthMesh = new THREE.Mesh(geometry, material);
    sceneRef.current.add(earthMesh);
    earthMeshRef.current = earthMesh;

    console.log('Scene setup complete');

  }, []);

  const animate = useCallback(() => {
    animationFrameIdRef.current = requestAnimationFrame(animate);
    if (earthMeshRef.current) {
      earthMeshRef.current.rotation.y += 0.005; // Slightly faster for debugging
    }
    if (rendererRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  }, []);

  const handleResize = useCallback(() => {
    if (mountRef.current && rendererRef.current && cameraRef.current) {
      const container = mountRef.current;
      const width = container.clientWidth;
      const height = container.clientHeight;

      rendererRef.current.setSize(width, height);
      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
    }
  }, []);

  useEffect(() => {
    const currentMountRef = mountRef.current;
    if (currentMountRef && !rendererRef.current) {
        console.log('Initializing RotatingEarthDebug component');
        setupScene(currentMountRef);
        animate();
    }
    
    window.addEventListener('resize', handleResize);

    return () => {
      console.log('Cleaning up RotatingEarthDebug component');
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      
      if (rendererRef.current) {
        rendererRef.current.dispose();
        if (rendererRef.current.domElement.parentNode === currentMountRef) {
             currentMountRef?.removeChild(rendererRef.current.domElement);
        }
      }
      sceneRef.current.traverse(object => {
        if (object instanceof THREE.Mesh) {
          object.geometry?.dispose();
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material?.dispose();
          }
        }
      });
      rendererRef.current = null;
      cameraRef.current = null;
      earthMeshRef.current = null;
    };
  }, [setupScene, animate, handleResize]);

  return (
    <div className="w-full h-full flex flex-col">
      <div ref={mountRef} className="w-full flex-grow" />
      <div className="text-xs text-white bg-black p-1">
        调试模式：蓝色球体应该在旋转
      </div>
    </div>
  );
};

export default RotatingEarthDebug;
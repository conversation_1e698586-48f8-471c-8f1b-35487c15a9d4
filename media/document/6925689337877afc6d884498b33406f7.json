{"name": "earth-&-solar-system-dynamics", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "three": "0.165.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/three": "^0.177.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}
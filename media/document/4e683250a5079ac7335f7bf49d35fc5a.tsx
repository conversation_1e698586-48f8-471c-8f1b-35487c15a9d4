import React, { useRef, useEffect, useCallback } from 'react';
import * as THREE from 'three';

// 使用参考实现中验证过的纹理URL
const EARTH_TEXTURE_URL = 'https://cdn.jsdelivr.net/gh/typpo/astronomy@master/src/astro/resources/earth_daymap.jpg';
// 备用纹理URL
const FALLBACK_TEXTURE_URL = 'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg';

const RotatingEarth: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const earthMeshRef = useRef<THREE.Mesh | null>(null);
  const sceneRef = useRef<THREE.Scene>(new THREE.Scene());

  const setupScene = useCallback((container: HTMLDivElement) => {
    const width = container.clientWidth;
    const height = container.clientHeight;

    console.log('设置地球场景，尺寸:', width, height);

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比例以提高性能
    renderer.setClearColor(0x000011, 1); // 深蓝色背景
    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    camera.position.set(0, 0, 3.5);

    // 添加基础光照
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    sceneRef.current.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(5, 3, 5);
    sceneRef.current.add(directionalLight);

    // 创建地球基础材质 - 使用MeshPhongMaterial，与参考实现保持一致
    const fallbackMaterial = new THREE.MeshPhongMaterial({
      color: 0x6699ff,
      specular: 0x111111,
      shininess: 5
    });

    // 创建地球几何体 - 使用与参考实现相同的参数
    const geometry = new THREE.SphereGeometry(1, 32, 32);
    
    // 创建地球网格
    const earthMesh = new THREE.Mesh(geometry, fallbackMaterial);
    sceneRef.current.add(earthMesh);
    earthMeshRef.current = earthMesh;

    // 异步加载纹理
    const loadTexture = (url: string, isBackup: boolean = false) => {
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load(
        url,
        (texture) => {
          console.log(`地球纹理加载成功 (${isBackup ? '备用' : '主要'}):`, url);
          
          // 设置纹理属性 - 关键是要使用RepeatWrapping而非ClampToEdgeWrapping
          texture.colorSpace = THREE.SRGBColorSpace;
          texture.wrapS = THREE.RepeatWrapping; 
          texture.wrapT = THREE.RepeatWrapping;
          
          // 创建材质
          const material = new THREE.MeshPhongMaterial({
            map: texture,
            specular: 0x111111,
            shininess: 5
          });
          
          // 更新网格材质
          if (earthMeshRef.current) {
            earthMeshRef.current.material = material;
          }
          
          // 添加参考纬线（与参考实现保持一致）
          addLatitudeLines(earthMeshRef.current);
        },
        undefined,
        (error) => {
          console.error(`地球纹理加载失败 (${isBackup ? '备用' : '主要'}):`, error);
          if (!isBackup) {
            console.log('尝试备用纹理...');
            loadTexture(FALLBACK_TEXTURE_URL, true);
          }
        }
      );
    };

    // 添加纬线辅助函数
    function addLatitudeLines(earth: THREE.Mesh) {
      // 添加纬线函数
      const addLatitudeLine = (lat: number, color: number) => {
        const latRad = lat * Math.PI / 180;
        const radius = Math.cos(latRad);
        const y = Math.sin(latRad);
        
        const points = [];
        for (let i = 0; i <= 64; i++) {
          const theta = (i / 64) * 2 * Math.PI;
          points.push(new THREE.Vector3(
            Math.cos(theta) * radius,
            y,
            Math.sin(theta) * radius
          ));
        }
        
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const lineMaterial = new THREE.LineBasicMaterial({ color });
        const line = new THREE.Line(lineGeometry, lineMaterial);
        earth.add(line);
      };

      // 添加轴线
      const axisGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(0, -1.3, 0),
        new THREE.Vector3(0, 1.3, 0)
      ]);
      const axisMaterial = new THREE.LineBasicMaterial({ color: 0x3399ff });
      const axisLine = new THREE.Line(axisGeometry, axisMaterial);
      earth.add(axisLine);

      // 添加纬线
      addLatitudeLine(45, 0xffff00);  // 北纬45度（黄色）
      addLatitudeLine(0, 0xff0000);   // 赤道（红色）
      addLatitudeLine(21, 0x00ff00);  // 北纬21度（绿色）
    }
    
    // 开始加载主纹理
    loadTexture(EARTH_TEXTURE_URL);
    
    return { camera };
  }, []);

  const animate = useCallback(() => {
    animationFrameIdRef.current = requestAnimationFrame(animate);
    
    if (earthMeshRef.current) {
      earthMeshRef.current.rotation.y += 0.002;
    }
    
    if (rendererRef.current && mountRef.current) {
      rendererRef.current.render(sceneRef.current, mountRef.current.camera);
    }
  }, []);

  const handleResize = useCallback(() => {
    if (mountRef.current && rendererRef.current && mountRef.current.camera) {
      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;

      rendererRef.current.setSize(width, height);
      mountRef.current.camera.aspect = width / height;
      mountRef.current.camera.updateProjectionMatrix();
    }
  }, []);

  useEffect(() => {
    const currentMountRef = mountRef.current;
    if (currentMountRef) {
      // 增加存储相机引用的属性
      currentMountRef.camera = setupScene(currentMountRef).camera;
      animate();
    }
    
    window.addEventListener('resize', handleResize);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      
      window.removeEventListener('resize', handleResize);
      
      // 清理Three.js对象
      if (rendererRef.current && currentMountRef) {
        if (rendererRef.current.domElement.parentNode === currentMountRef) {
          currentMountRef.removeChild(rendererRef.current.domElement);
        }
        rendererRef.current.dispose();
      }
      
      // 清理场景中的对象
      sceneRef.current.traverse(object => {
        if (object instanceof THREE.Mesh) {
          object.geometry?.dispose();
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material?.dispose();
          }
        }
      });
    };
  }, [setupScene, animate, handleResize]);

  return <div ref={mountRef} className="w-full h-full" />;
};

export default RotatingEarth;
{"originHash": "0c9b009be861728887f2aeb221ea0e949d1ec037e6be2256bd1290196b5ce341", "pins": [{"identity": "libtessswift", "kind": "remoteSourceControl", "location": "https://github.com/LuizZak/LibTessSwift.git", "state": {"revision": "da48d3f8e66a614df22d5b2fab2f04576130f46e", "version": "0.8.2"}}, {"identity": "minilexer", "kind": "remoteSourceControl", "location": "https://github.com/LuizZak/MiniLexer.git", "state": {"revision": "4da97d6f92d79402cd3cf35d6438ec723554ae7c", "version": "0.9.5"}}], "version": 3}
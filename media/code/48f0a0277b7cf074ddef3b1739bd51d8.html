{% extends "base.html" %}

{% block title %}{{ entry.title }} - 技术知识管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <!-- 知识条目主要内容 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-start">
                    <h1 class="mb-0">{{ entry.title }}</h1>
                    <span class="badge bg-{{ 'success' if entry.difficulty_level == 'beginner' else 'warning' if entry.difficulty_level == 'intermediate' else 'danger' }} difficulty-badge">
                        {{ {'beginner': '初级', 'intermediate': '中级', 'advanced': '高级'}[entry.difficulty_level] }}
                    </span>
                </div>
                
                {% if entry.summary %}
                <p class="text-muted mt-2 mb-0">{{ entry.summary }}</p>
                {% endif %}
            </div>
            
            <div class="card-body">
                <!-- 主要内容 -->
                <div class="knowledge-content">
                    {{ entry.content_html | safe }}
                </div>
                
                <!-- 标签和分类 -->
                <div class="mt-4 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if entry.category_name %}
                            <span class="badge bg-primary me-2">{{ entry.category_name }}</span>
                            {% endif %}
                            
                            {% for tag in entry.tags | format_tags %}
                            <span class="tag">{{ tag }}</span>
                            {% endfor %}
                        </div>
                        
                        <small class="text-muted">
                            创建于 {{ entry.created_at.strftime('%Y-%m-%d %H:%M') }}
                            {% if entry.updated_at != entry.created_at %}
                            <br>更新于 {{ entry.updated_at.strftime('%Y-%m-%d %H:%M') }}
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 代码示例 -->
        {% if entry.code_examples %}
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-code"></i> 代码示例
                </h3>
            </div>
            <div class="card-body p-0">
                {% for code in entry.code_examples %}
                <div class="code-example">
                    <div class="code-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ code.title }}</h5>
                            <span class="badge bg-secondary">{{ code.language }}</span>
                        </div>
                        {% if code.description %}
                        <p class="text-muted mb-0 mt-1">{{ code.description }}</p>
                        {% endif %}
                    </div>
                    <div class="code-content">
                        {% if code.highlighted_code %}
                            {{ code.highlighted_code | safe }}
                        {% else %}
                            <pre><code class="language-{{ code.language }}">{{ code.code_content }}</code></pre>
                        {% endif %}
                    </div>
                </div>
                {% if not loop.last %}<hr class="my-0">{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- 图表演示 -->
        {% if entry.diagrams %}
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-chart-line"></i> 图表演示
                </h3>
            </div>
            <div class="card-body">
                {% for diagram in entry.diagrams %}
                <div class="diagram-section mb-4">
                    <h5>{{ diagram.title }}</h5>
                    {% if diagram.description %}
                    <p class="text-muted">{{ diagram.description }}</p>
                    {% endif %}
                    
                    {% if diagram.diagram_type == 'mermaid' %}
                    <div class="mermaid-diagram">
                        <div class="mermaid-code" style="display: none;">{{ diagram.diagram_definition }}</div>
                    </div>
                    {% else %}
                    <pre><code>{{ diagram.diagram_definition }}</code></pre>
                    {% endif %}
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- 相关链接 -->
        {% if entry.related_links %}
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-external-link-alt"></i> 相关链接
                </h3>
            </div>
            <div class="card-body">
                {% for link in entry.related_links %}
                <div class="mb-2">
                    <a href="{{ link.url }}" target="_blank" class="text-decoration-none">
                        <i class="fas fa-link"></i> {{ link.title }}
                    </a>
                    <span class="badge bg-light text-dark ms-2">{{ link.link_type }}</span>
                    {% if link.description %}
                    <p class="text-muted small mb-0 mt-1">{{ link.description }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- 侧边栏 -->
    <div class="col-md-4">
        <!-- 导航 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">快速导航</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    <a href="{{ url_for('search') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-search"></i> 继续搜索
                    </a>
                    {% if entry.category_name %}
                    <a href="{{ url_for('search', category=entry.category_name) }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-folder"></i> 浏览 {{ entry.category_name }}
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 知识统计 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">内容统计</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary">{{ entry.code_examples | length }}</div>
                        <small class="text-muted">代码示例</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success">{{ entry.diagrams | length }}</div>
                        <small class="text-muted">图表演示</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 相关标签 -->
        {% if entry.tags | format_tags %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">相关标签</h5>
            </div>
            <div class="card-body">
                {% for tag in entry.tags | format_tags %}
                <a href="{{ url_for('search', q=tag, type='tag') }}" class="tag text-decoration-none">{{ tag }}</a>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- 使用提示 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">使用提示</h5>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>点击代码块可以复制代码</li>
                    <li>图表支持缩放和交互</li>
                    <li>点击标签可以查找相关内容</li>
                    <li>使用浏览器书签保存重要知识</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 代码复制功能
document.addEventListener('DOMContentLoaded', function() {
    // 为所有代码块添加复制按钮
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(function(codeBlock) {
        const pre = codeBlock.parentElement;
        const copyButton = document.createElement('button');
        copyButton.className = 'btn btn-sm btn-outline-secondary position-absolute top-0 end-0 m-2';
        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        copyButton.title = '复制代码';
        
        pre.style.position = 'relative';
        pre.appendChild(copyButton);
        
        copyButton.addEventListener('click', function() {
            navigator.clipboard.writeText(codeBlock.textContent).then(function() {
                copyButton.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(function() {
                    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            });
        });
    });
});
</script>
{% endblock %}

//
//  Shaders.metal
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

#include <metal_stdlib>
using namespace metal;

// MARK: - 顶点结构定义

struct Vertex3D {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
};

// 用于轨道和文字的简化3D顶点
struct SimpleVertex3D {
    float3 position [[attribute(0)]];
};

struct Transform {
    float4x4 modelMatrix;
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
};

// MARK: - 顶点着色器输出结构

struct VertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 normal;
};

struct SimpleVertexOut {
    float4 position [[position]];
};

// MARK: - 球体渲染着色器

vertex VertexOut sphere_vertex(Vertex3D in [[stage_in]],
                              constant Transform& transform [[buffer(1)]],
                              constant float4x4& modelMatrix [[buffer(2)]]) {
    VertexOut out;
    
    // 计算完整的变换矩阵
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    
    // 变换顶点位置
    float4 worldPosition = modelMatrix * float4(in.position, 1.0);
    out.position = mvpMatrix * float4(in.position, 1.0);
    out.worldPosition = worldPosition.xyz;
    
    // 变换法向量
    float3x3 normalMatrix = float3x3(modelMatrix[0].xyz, modelMatrix[1].xyz, modelMatrix[2].xyz);
    out.normal = normalize(normalMatrix * in.normal);
    
    return out;
}

fragment float4 sphere_fragment(VertexOut in [[stage_in]],
                               constant float4& color [[buffer(0)]]) {
    // 简单的Lambert光照模型
    float3 lightDirection = normalize(float3(1.0, 1.0, 1.0));
    float3 normal = normalize(in.normal);
    
    // 环境光和漫反射光
    float ambient = 0.3;
    float diffuse = max(dot(normal, lightDirection), 0.0);
    float intensity = ambient + diffuse * 0.7;
    
    return float4(color.rgb * intensity, color.a);
}

// MARK: - 简单3D渲染着色器（用于轨道和文字）

vertex SimpleVertexOut simple_vertex(SimpleVertex3D in [[stage_in]],
                                     constant Transform& transform [[buffer(1)]],
                                     constant float4x4& modelMatrix [[buffer(2)]]) {
    SimpleVertexOut out;

    // 计算完整的变换矩阵
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;

    // 变换顶点位置
    out.position = mvpMatrix * float4(in.position, 1.0);

    return out;
}

fragment float4 simple_fragment(SimpleVertexOut in [[stage_in]],
                               constant float4& color [[buffer(0)]]) {
    return color;
}
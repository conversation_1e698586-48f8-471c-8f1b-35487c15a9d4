import Cocoa
import MetalKit
import Metal

class ViewController: NSViewController {
    
    var metalView: MTKView!
    var renderer: SolarSystemRenderer!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupMetalView()
        setupRenderer()
    }
    
    private func setupMetalView() {
        guard let device = MTLCreateSystemDefaultDevice() else {
            fatalError("Metal is not supported on this device")
        }
        
        metalView = MTKView(frame: view.bounds, device: device)
        metalView.translatesAutoresizingMaskIntoConstraints = false
        metalView.clearColor = MTLClearColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0) // 白色背景
        metalView.depthStencilPixelFormat = .depth32Float
        metalView.preferredFramesPerSecond = 60
        
        view.addSubview(metalView)
        
        // 约束设置
        NSLayoutConstraint.activate([
            metalView.topAnchor.constraint(equalTo: view.topAnchor),
            metalView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            metalView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            metalView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupRenderer() {
        guard let device = metalView.device else {
            fatalError("Metal device not found")
        }
        
        renderer = SolarSystemRenderer(device: device, view: metalView)
        metalView.delegate = renderer
    }    
    override func viewDidLayout() {
        super.viewDidLayout()
        metalView.frame = view.bounds
    }
}
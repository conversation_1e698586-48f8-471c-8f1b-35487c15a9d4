#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保存Metal相关技术知识到数据库
包含我们刚才讨论的坐标系和深度测试知识
"""

from knowledge_manager import KnowledgeManager
import json

def save_metal_coordinate_system():
    """保存Metal坐标系知识"""
    km = KnowledgeManager()
    
    # 1. 保存Metal坐标系主要知识
    coordinate_id = km.save_knowledge_entry(
        title="Metal 3D坐标系详解",
        summary="详细解释Metal中3D坐标系的设计原理，包括坐标变换流程、NDC坐标系特点、实际应用等",
        content="""# Metal 3D坐标系详解

## 坐标系层次结构

在Metal 3D图形渲染中，有三个主要的坐标系变换层次：

```
世界坐标 → 视图坐标 → 屏幕坐标
(World)   (View)     (Screen)
```

## 坐标系变换流程

完整的3D图形管线变换流程：

1. **本地坐标系 (Local Space)** - 每个3D对象自己的坐标系
2. **世界坐标系 (World Space)** - 通过模型矩阵变换
3. **视图坐标系 (View Space)** - 通过视图矩阵变换  
4. **裁剪坐标系 (Clip Space)** - 通过投影矩阵变换
5. **标准化设备坐标 (NDC)** - 透视除法后的结果
6. **屏幕坐标系 (Screen Space)** - 最终显示坐标

## NDC坐标系特点

Metal使用的NDC坐标系：
- **X轴**: -1.0 (左) 到 +1.0 (右)
- **Y轴**: -1.0 (下) 到 +1.0 (上)  
- **Z轴**: 0.0 (近) 到 1.0 (远)

## 实际应用示例

在太阳系应用中的布局设计：
- 太阳系偏移：`solarSystemOffsetX = -1.2`（向左偏移）
- 文字偏移：`textOffsetX = 1.2`（向右偏移）
- 扩大视野：`viewScale = 2.0`（容纳左右分布的内容）

## 变换矩阵计算

着色器中的完整变换：
```metal
float4x4 mvpMatrix = projectionMatrix * viewMatrix * modelMatrix;
out.position = mvpMatrix * float4(in.position, 1.0);
```

这种设计的优势：
1. **统一性** - 所有对象都在同一个3D坐标系中
2. **灵活性** - 可以轻松调整布局、添加新对象  
3. **性能** - Metal的GPU并行处理3D变换非常高效
4. **扩展性** - 未来可以添加相机控制、3D旋转等功能""",
        category_name="Metal编程",
        tags=["坐标系", "3D变换", "NDC", "变换矩阵", "Metal"],
        difficulty="intermediate",
        source_type="ai_explanation"
    )
    
    # 保存着色器代码示例
    km.save_code_example(
        knowledge_entry_id=coordinate_id,
        title="Metal着色器坐标变换",
        language="metal",
        code_content="""// Metal着色器中的坐标变换实现
vertex VertexOut sphere_vertex(Vertex3D in [[stage_in]],
                              constant Transform& transform [[buffer(1)]],
                              constant float4x4& modelMatrix [[buffer(2)]]) {
    VertexOut out;
    
    // 计算完整的变换矩阵：投影 × 视图 × 世界 × 本地
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    
    // 变换顶点位置
    out.position = mvpMatrix * float4(in.position, 1.0);
    
    // 变换法向量（用于光照计算）
    float3x3 normalMatrix = float3x3(modelMatrix[0].xyz, modelMatrix[1].xyz, modelMatrix[2].xyz);
    out.normal = normalize(normalMatrix * in.normal);
    
    return out;
}""",
        description="展示Metal着色器中如何实现完整的3D坐标变换，包括位置和法向量的变换"
    )
    
    # 保存Swift代码示例
    km.save_code_example(
        knowledge_entry_id=coordinate_id,
        title="Swift中的变换矩阵计算",
        language="swift",
        code_content="""// Swift中计算变换矩阵的示例
private func updateTransforms() {
    let aspectRatio = Float(mtkView.drawableSize.width / mtkView.drawableSize.height)
    
    // 投影矩阵（正交投影）- 扩大视野以容纳左右分布的内容
    let viewScale: Float = 2.0 // 扩大视野
    let projectionMatrix = orthographicProjection(
        left: -aspectRatio * viewScale, 
        right: aspectRatio * viewScale,
        bottom: -1.0 * viewScale, 
        top: 1.0 * viewScale,
        near: -1.0, 
        far: 1.0
    )
    
    // 视图矩阵（单位矩阵 - 相机在原点）
    let viewMatrix = matrix_identity_float4x4
    
    // 模型矩阵（单位矩阵 - 基础变换）
    let modelMatrix = matrix_identity_float4x4
    
    let transform = Transform(
        modelMatrix: modelMatrix,
        viewMatrix: viewMatrix,
        projectionMatrix: projectionMatrix
    )
    
    // 更新GPU缓冲区
    let transformPointer = transformBuffer?.contents().bindMemory(to: Transform.self, capacity: 1)
    transformPointer?.pointee = transform
}""",
        description="展示如何在Swift中计算和更新3D变换矩阵"
    )
    
    # 保存坐标系变换流程图
    km.save_diagram(
        knowledge_entry_id=coordinate_id,
        title="Metal 3D坐标系变换流程",
        diagram_type="mermaid",
        diagram_definition="""graph TD
    A[本地坐标系<br/>Local Space] --> B[模型矩阵<br/>Model Matrix]
    B --> C[世界坐标系<br/>World Space]
    C --> D[视图矩阵<br/>View Matrix]
    D --> E[视图坐标系<br/>View Space]
    E --> F[投影矩阵<br/>Projection Matrix]
    F --> G[裁剪坐标系<br/>Clip Space]
    G --> H[透视除法<br/>Perspective Division]
    H --> I[标准化设备坐标<br/>NDC Space]
    I --> J[视口变换<br/>Viewport Transform]
    J --> K[屏幕坐标系<br/>Screen Space]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style E fill:#e8f5e8
    style I fill:#fff3e0
    style K fill:#ffebee""",
        description="展示Metal中3D坐标系的完整变换流程，从本地坐标到屏幕坐标的每个步骤"
    )
    
    print(f"✅ 已保存Metal坐标系知识，ID: {coordinate_id}")
    km.close()
    return coordinate_id

def save_depth_testing_knowledge():
    """保存深度测试知识"""
    km = KnowledgeManager()
    
    # 保存深度测试主要知识
    depth_id = km.save_knowledge_entry(
        title="Metal深度测试详解",
        summary="深入解释Metal中深度测试的工作原理、配置方法和性能优化，包括Early Z-Test等高级概念",
        content="""# Metal深度测试详解

## 什么是深度测试？

深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。

## 深度测试工作原理

### 深度缓冲区 (Depth Buffer)
- 每个像素都有一个对应的深度值（Z值）
- 使用`.depth32Float`格式，提供32位浮点精度
- 深度值范围：0.0（最近）到 1.0（最远）

### 深度比较过程
```
if (新像素的深度值 < 深度缓冲区中的深度值) {
    // 新像素更近，更新颜色缓冲区和深度缓冲区
    渲染新像素
    更新深度缓冲区 = 新像素的深度值
} else {
    // 新像素更远，丢弃
    丢弃新像素
}
```

## Metal中的深度测试配置

### 1. 深度缓冲区配置
```swift
metalView.depthStencilPixelFormat = .depth32Float
```

### 2. 深度测试状态配置
```swift
let depthStencilDescriptor = MTLDepthStencilDescriptor()
depthStencilDescriptor.depthCompareFunction = .less  // 更近的像素通过
depthStencilDescriptor.isDepthWriteEnabled = true    // 更新深度缓冲区
```

## 深度比较函数选项

- `.less` - 更近的像素通过（最常用）
- `.greater` - 更远的像素通过（特殊效果）
- `.equal` - 相同深度的像素通过
- `.always` - 所有像素都通过（关闭深度测试）
- `.never` - 所有像素都不通过

## 性能优势：Early Z-Test

现代GPU在片段着色器运行**之前**就进行深度测试：

```
顶点着色器 → 光栅化 → 早期深度测试 → 片段着色器（如果通过）
```

这意味着被遮挡的像素根本不会运行昂贵的片段着色器，大大提高性能。

## 实际应用示例

在太阳系应用中，深度测试确保：

1. **地球绕太阳运动时**：
   - 当地球在太阳前面时，地球遮挡太阳
   - 当地球在太阳后面时，太阳遮挡地球

2. **轨道线的正确显示**：
   - 轨道线在太阳和地球的同一深度平面
   - 不会被错误地遮挡或显示

3. **文字的正确显示**：
   - 文字在最前面的深度层
   - 不会被太阳系对象遮挡

## 深度值计算

深度值在着色器中自动计算：
- MVP变换后，`position.z`就是深度值
- Metal自动进行透视除法和深度范围映射
- 最终深度值用于深度测试比较""",
        category_name="Metal编程",
        tags=["深度测试", "深度缓冲", "Z-Buffer", "Early Z-Test", "Metal", "3D渲染"],
        difficulty="intermediate",
        source_type="ai_explanation"
    )
    
    # 保存深度测试配置代码
    km.save_code_example(
        knowledge_entry_id=depth_id,
        title="Metal深度测试配置",
        language="swift",
        code_content="""// Metal深度测试完整配置示例

// 1. MTKView深度缓冲配置
private func setupMetalView() {
    metalView.depthStencilPixelFormat = .depth32Float  // 32位浮点深度精度
    metalView.clearColor = MTLClearColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
    metalView.preferredFramesPerSecond = 60
}

// 2. 深度模板状态配置
private func setupDepthStencilState() {
    let depthStencilDescriptor = MTLDepthStencilDescriptor()
    
    // 深度比较函数：更近的像素通过测试
    depthStencilDescriptor.depthCompareFunction = .less
    
    // 启用深度写入：通过测试的像素更新深度缓冲区
    depthStencilDescriptor.isDepthWriteEnabled = true
    
    // 创建深度模板状态
    depthStencilState = device.makeDepthStencilState(descriptor: depthStencilDescriptor)
}

// 3. 渲染时应用深度测试
func draw(in view: MTKView) {
    guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
        return
    }
    
    // 设置深度模板状态
    renderEncoder.setDepthStencilState(depthStencilState)
    
    // 渲染3D对象...
    renderSun(encoder: renderEncoder)
    renderEarth(encoder: renderEncoder)
    
    renderEncoder.endEncoding()
}""",
        description="展示Metal中深度测试的完整配置流程，从MTKView设置到渲染应用"
    )
    
    # 保存深度测试工作流程图
    km.save_diagram(
        knowledge_entry_id=depth_id,
        title="深度测试工作流程",
        diagram_type="mermaid",
        diagram_definition="""graph TD
    A[3D场景中的多个对象] --> B[光栅化为像素]
    B --> C[每个像素都有深度值Z]
    C --> D[深度缓冲区存储最近的Z值]
    D --> E[新像素与缓冲区比较]
    E --> F{新像素更近?}
    F -->|是| G[更新颜色缓冲区和深度缓冲区]
    F -->|否| H[丢弃新像素]
    G --> I[最终渲染结果]
    H --> I
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style F fill:#fff3e0
    style I fill:#e8f5e8""",
        description="展示深度测试的完整工作流程，从3D对象到最终渲染结果"
    )
    
    print(f"✅ 已保存深度测试知识，ID: {depth_id}")
    km.close()
    return depth_id

def create_metal_learning_path():
    """创建Metal学习路径"""
    km = KnowledgeManager()
    
    # 获取已保存的知识ID
    km.cursor.execute("SELECT id FROM knowledge_entries WHERE title LIKE '%Metal%' ORDER BY id")
    knowledge_ids = [row['id'] for row in km.cursor.fetchall()]
    
    if knowledge_ids:
        path_id = km.create_learning_path(
            name="Metal 3D图形编程入门",
            description="从基础概念到实际应用，系统学习Metal 3D图形编程。包括坐标系统、深度测试、着色器编程等核心概念。",
            knowledge_ids=knowledge_ids,
            difficulty="intermediate"
        )
        print(f"✅ 已创建Metal学习路径，ID: {path_id}")
        km.close()
        return path_id
    else:
        print("❌ 没有找到Metal相关知识条目")
        km.close()
        return None

if __name__ == "__main__":
    print("🚀 开始保存Metal技术知识...")
    
    # 保存知识条目
    coord_id = save_metal_coordinate_system()
    depth_id = save_depth_testing_knowledge()
    
    # 创建学习路径
    path_id = create_metal_learning_path()
    
    print("\n✨ 知识保存完成！")
    print(f"📚 坐标系知识ID: {coord_id}")
    print(f"🔍 深度测试知识ID: {depth_id}")
    if path_id:
        print(f"🛤️  学习路径ID: {path_id}")
    
    print("\n🌐 启动Web应用查看：python knowledge_web_app.py")
    print("📖 访问 http://localhost:5000 浏览知识库")

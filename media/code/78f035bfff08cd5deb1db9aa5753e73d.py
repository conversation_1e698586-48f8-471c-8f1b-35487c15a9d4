#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速上传脚本
双击运行，自动选择文件夹并上传
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess
import sys
import os

def quick_upload():
    """快速上传函数"""
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()
    
    # 显示欢迎消息
    result = messagebox.askyesno(
        "文件夹快速上传", 
        "欢迎使用文件夹快速上传工具！\n\n"
        "这个工具可以将您的项目文件夹批量上传到知识管理系统。\n\n"
        "点击'是'选择要上传的文件夹\n"
        "点击'否'退出程序"
    )
    
    if not result:
        return
    
    # 选择文件夹
    folder_path = filedialog.askdirectory(
        title="选择要上传的项目文件夹",
        initialdir=os.path.expanduser("~/Desktop")  # 默认从桌面开始
    )
    
    if not folder_path:
        messagebox.showinfo("取消", "未选择文件夹，程序退出。")
        return
    
    # 确认上传
    folder_name = os.path.basename(folder_path)
    confirm = messagebox.askyesno(
        "确认上传",
        f"确定要上传文件夹吗？\n\n"
        f"文件夹: {folder_name}\n"
        f"路径: {folder_path}\n\n"
        f"系统将自动:\n"
        f"• 扫描并上传支持的文件\n"
        f"• 创建对应的知识条目\n"
        f"• 在浏览器中显示结果\n\n"
        f"点击'是'开始上传"
    )
    
    if not confirm:
        return
    
    try:
        # 检查上传脚本是否存在
        uploader_script = "desktop_folder_uploader.py"
        if not os.path.exists(uploader_script):
            messagebox.showerror(
                "错误", 
                f"找不到上传脚本: {uploader_script}\n\n"
                f"请确保以下文件在同一目录:\n"
                f"• quick_upload.py\n"
                f"• desktop_folder_uploader.py\n"
                f"• simple_app.py"
            )
            return
        
        # 显示进度窗口
        progress_window = tk.Toplevel(root)
        progress_window.title("正在上传...")
        progress_window.geometry("400x200")
        progress_window.resizable(False, False)
        
        # 居中显示
        progress_window.transient(root)
        progress_window.grab_set()
        
        # 进度信息
        tk.Label(progress_window, text="📤 正在上传文件夹", 
                font=('Arial', 14, 'bold')).pack(pady=20)
        
        tk.Label(progress_window, text=f"文件夹: {folder_name}", 
                font=('Arial', 10)).pack(pady=5)
        
        tk.Label(progress_window, text="请稍候，上传过程可能需要几分钟...", 
                font=('Arial', 10), foreground='blue').pack(pady=10)
        
        # 进度条（不定长度）
        import tkinter.ttk as ttk
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=20, padx=40, fill='x')
        progress_bar.start()
        
        # 状态标签
        status_label = tk.Label(progress_window, text="正在扫描文件...", 
                               font=('Arial', 9), foreground='gray')
        status_label.pack(pady=5)
        
        progress_window.update()
        
        # 执行上传
        cmd = [sys.executable, uploader_script, folder_path]
        
        # 使用subprocess运行上传脚本
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            text=True,
            universal_newlines=True
        )
        
        # 等待完成
        stdout, stderr = process.communicate()
        
        # 关闭进度窗口
        progress_window.destroy()
        
        if process.returncode == 0:
            # 上传成功
            # 从输出中提取知识条目ID
            knowledge_id = None
            for line in stdout.split('\n'):
                if '知识条目ID:' in line:
                    try:
                        knowledge_id = line.split(':')[1].strip()
                        break
                    except:
                        pass
            
            success_msg = f"🎉 文件夹上传成功！\n\n"
            success_msg += f"文件夹: {folder_name}\n"
            if knowledge_id:
                success_msg += f"知识条目ID: {knowledge_id}\n"
            success_msg += f"\n是否在浏览器中查看上传结果？"
            
            if messagebox.askyesno("上传成功", success_msg):
                # 打开浏览器
                import webbrowser
                if knowledge_id:
                    webbrowser.open(f"http://localhost:8081/knowledge/{knowledge_id}")
                else:
                    webbrowser.open("http://localhost:8081")
        else:
            # 上传失败
            error_msg = f"❌ 上传失败\n\n"
            if stderr:
                error_msg += f"错误信息:\n{stderr}\n\n"
            if stdout:
                error_msg += f"详细输出:\n{stdout}"
            
            messagebox.showerror("上传失败", error_msg)
    
    except Exception as e:
        messagebox.showerror("错误", f"程序运行出错:\n{e}")
    
    finally:
        root.destroy()

def check_dependencies():
    """检查依赖"""
    try:
        import requests
        import mysql.connector
        return True
    except ImportError as e:
        messagebox.showerror(
            "缺少依赖", 
            f"缺少必要的Python包:\n{e}\n\n"
            f"请运行以下命令安装:\n"
            f"pip install requests mysql-connector-python"
        )
        return False

def check_server():
    """检查服务器是否运行"""
    try:
        import requests
        response = requests.get("http://localhost:8081/api/test", timeout=3)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查服务器
    if not check_server():
        root = tk.Tk()
        root.withdraw()
        
        result = messagebox.askyesno(
            "服务器未运行",
            "知识管理系统服务器似乎没有运行。\n\n"
            "请先启动服务器:\n"
            "python simple_app.py\n\n"
            "点击'是'继续尝试上传\n"
            "点击'否'退出程序"
        )
        
        if not result:
            return
    
    # 执行快速上传
    quick_upload()

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>Earth Orbit Simulation – 节气 & 月份 - 跑道布局</title>
  <style>
    html,body{margin:0;height:100%;overflow:hidden;background:#000;}
    #overlay{position:fixed;top:0;left:0;right:0;pointer-events:none;font:14px/1.4em sans-serif;color:#fff;}
    #overlay .title{padding:8px 12px;}
  </style>
  <script type="importmap">
    {"imports":{"three":"https://unpkg.com/three@0.160.0/build/three.module.js","three/addons/":"https://unpkg.com/three@0.160.0/examples/jsm/"}}
  </script>
</head>
<body>
<div id="overlay"><div class="title">左：地球自转  右：公转与季节  数轴：节气 & 月份 - 光照跑道布局</div></div>
<canvas id="c"></canvas>
<canvas id="chart" width="640" height="800" style="position:fixed;top:40px;right:0;left:auto;height:800px;background:rgba(0,0,0,0.35);"></canvas>

<script type="module">
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { CSS2DRenderer, CSS2DObject } from 'three/addons/renderers/CSS2DRenderer.js';

const canvas3D = document.getElementById('c');
const scene    = new THREE.Scene();
const camera   = new THREE.PerspectiveCamera(45, innerWidth/innerHeight, 0.1, 1e6);
const renderer = new THREE.WebGLRenderer({canvas:canvas3D,antialias:true});
renderer.setPixelRatio(devicePixelRatio);
renderer.setSize(innerWidth,innerHeight);
renderer.toneMappingExposure = 1.2;
// 初始化CSS2DRenderer
const labelRenderer = new CSS2DRenderer();
labelRenderer.setSize(window.innerWidth, window.innerHeight);
labelRenderer.domElement.style.position = 'absolute';
labelRenderer.domElement.style.top = '0px';
labelRenderer.domElement.style.pointerEvents = 'none';
document.body.appendChild(labelRenderer.domElement);
const controls = new OrbitControls(camera,renderer.domElement);
controls.enableDamping=true;

const EARTH_R=0.2, SUN_R=EARTH_R*109.3;
const ORBIT_R=EARTH_R*23465*0.5; 
const BIG_R=EARTH_R*23465*0.32; 
const OFFSET=ORBIT_R*0.6;
const YEAR_S = 60, DAY_S = 2;
const OBLIQ =THREE.MathUtils.degToRad(23.44);

camera.position.set(0,ORBIT_R*1.2,ORBIT_R*3.6);
camera.lookAt(0,0,0);

const leftG = new THREE.Group(); leftG.position.x = -OFFSET * 3.0;
const rightG= new THREE.Group(); rightG.position.x =  OFFSET * 0.2;
scene.add(leftG, rightG);

const loader=new THREE.TextureLoader();
const earthMat=new THREE.MeshPhongMaterial({color:0x8888ff});
loader.load('https://cdn.jsdelivr.net/gh/typpo/astronomy@master/src/astro/resources/earth_daymap.jpg',t=>{t.colorSpace=THREE.SRGBColorSpace;earthMat.map=t;earthMat.needsUpdate=true;});
const sunMat = new THREE.MeshBasicMaterial({color:0xffff66});

const sunMesh=new THREE.Mesh(new THREE.SphereGeometry(SUN_R,32,32),sunMat);
rightG.add(sunMesh,new THREE.PointLight(0xffffff,3,0,2));
const earthOrbitG=new THREE.Group();
const tiltG=new THREE.Group(); tiltG.rotation.x=OBLIQ;
const earthMesh=new THREE.Mesh(new THREE.SphereGeometry(EARTH_R,32,32),earthMat);
earthMesh.add(new THREE.Line(
  new THREE.BufferGeometry().setFromPoints([
    new THREE.Vector3(0, -EARTH_R * 1.7, 0),
    new THREE.Vector3(0,  EARTH_R * 1.7, 0)
  ]),
  new THREE.LineBasicMaterial({color: 0xffffff, linewidth: 5})
));
tiltG.add(earthMesh);
earthOrbitG.add(tiltG);
rightG.add(earthOrbitG);

const SEG=256, orbitPts=[];
for(let i=0;i<=SEG;i++){const th=i/SEG*Math.PI*2;orbitPts.push(new THREE.Vector3(Math.cos(th)*ORBIT_R,0,Math.sin(th)*ORBIT_R));}
rightG.add(new THREE.LineLoop(new THREE.BufferGeometry().setFromPoints(orbitPts),new THREE.LineDashedMaterial({color:0x444444,dashSize:2,gapSize:2})));

const conn=new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(),new THREE.Vector3()]),new THREE.LineDashedMaterial({color:0xffffff,dashSize:EARTH_R*2,gapSize:EARTH_R*2}));
rightG.add(conn);

const leftTilt=new THREE.Group(); leftTilt.rotation.set(0,0,OBLIQ);
const bigEarth = new THREE.Mesh(
  new THREE.SphereGeometry(BIG_R, 64, 64),
  new THREE.MeshPhongMaterial({
    color: 0x8888ff,
    specular: 0x111111,
    shininess: 5
  })
);
leftTilt.add(bigEarth);
// 工具函数：在bigEarth上画纬线
function addLatitudeLine(bigEarth, latDeg, color) {
  const lat = latDeg * Math.PI / 180;
  const r = BIG_R * Math.cos(lat);
  const y = BIG_R * Math.sin(lat);
  const segments = 128;
  const points = [];
  for (let i = 0; i <= segments; i++) {
    const theta = (i / segments) * 2 * Math.PI;
    points.push(new THREE.Vector3(
      Math.cos(theta) * r,
      y,
      Math.sin(theta) * r
    ));
  }
  const geo = new THREE.BufferGeometry().setFromPoints(points);
  const mat = new THREE.LineBasicMaterial({color: color});
  const line = new THREE.Line(geo, mat);
  bigEarth.add(line);
}
// 北纬45°（黄色）
addLatitudeLine(bigEarth, 45, 0xffff00);
// 赤道（北纬0°，红色）
addLatitudeLine(bigEarth, 0, 0xff0000);
// 北纬21°（绿色）
addLatitudeLine(bigEarth, 21, 0x00ff00);

// 工具函数：使用CSS2DObject为每个纬线添加标签（固定，不随地球自转）
function addLatitudeLabel2(parent, latDeg, color, text) {
  const lat = latDeg * Math.PI / 180; // 计算纬度角度
  const r = BIG_R * Math.cos(lat);
  const y = BIG_R * Math.sin(lat);
  // 固定在经度0°（地球右侧）
  const pos = new THREE.Vector3(r, y, 0); 
  const div = document.createElement('div');
  div.className = 'lat-label';
  div.textContent = text;
  div.style.color = color;
  div.style.fontWeight = 'bold';
  div.style.fontSize = '24px';
  div.style.textShadow = '0 0 6px #000,0 0 2px #000';
  div.style.pointerEvents = 'none';
  const label = new CSS2DObject(div);
  label.position.copy(pos);
  parent.add(label);
}
// 添加标签
addLatitudeLabel2(leftTilt, 45, '#ffff00', '45°N');
addLatitudeLabel2(leftTilt, 21, '#00ff00', '21°N');
addLatitudeLabel2(leftTilt, 0, '#ff0000', '0°');

// 为大球贴地球纹理
const earthTextureUrl = 'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg';
new THREE.TextureLoader().load(earthTextureUrl, function(tex) {
  bigEarth.material.map = tex;
  bigEarth.material.needsUpdate = true;
});
leftTilt.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,-BIG_R*1.3,0),new THREE.Vector3(0,BIG_R*1.3,0)]),new THREE.LineBasicMaterial({color:0x3399ff})));
leftG.add(leftTilt);

const leftSunDir=new THREE.DirectionalLight(0xffffff,30);
scene.add(leftSunDir,leftSunDir.target);
loader.load('https://threejs.org/examples/textures/galaxy_starfield.png',tx=>{scene.add(new THREE.Mesh(new THREE.SphereGeometry(OFFSET*4,64,64),new THREE.MeshBasicMaterial({map:tx,side:THREE.BackSide})));});

const chart=document.getElementById('chart');
const ctx=chart.getContext('2d'); let W=chart.width,H=chart.height; const M=45;
const X_MIN=-23.44, X_MAX=23.44, Y_MIN=0, Y_MAX=100;
// 北半球光照动态范围（基于OBLIQ=23.44°）
const INS_MAX = 50 + 50 * Math.sin(23.44 * Math.PI / 180);
const INS_MIN = 50 - 50 * Math.sin(23.44 * Math.PI / 180);
const mapX=x=>M+(x-X_MIN)/(X_MAX-X_MIN)*(W-2*M);
const mapY=y=>M+(1-(y-Y_MIN)/(Y_MAX-Y_MIN))*(H-2*M);

const termNames=['立春','雨水','惊蛰','春分','清明','谷雨','立夏','小满','芒种','夏至','小暑','大暑','立秋','处暑','白露','秋分','寒露','霜降','立冬','小雪','大雪','冬至','小寒','大寒'];
const term2month={'小寒':1,'大寒':1,'立春':2,'雨水':2,'惊蛰':3,'春分':3,'清明':4,'谷雨':4,'立夏':5,'小满':5,'芒种':6,'夏至':6,'小暑':7,'大暑':7,'立秋':8,'处暑':8,'白露':9,'秋分':9,'寒露':10,'霜降':10,'立冬':11,'小雪':11,'大雪':12,'冬至':12};
const Ls0=0;
const sunLonByTerm=i=>(Ls0+i*15)%360;
const termIndexBySunLon=Ls=>Math.round(((Ls-Ls0+360)%360)/15)%24;

function insolationFraction(LsDeg) {
  const OBLIQ = 23.44;
  const delta = OBLIQ * Math.sin(LsDeg * Math.PI / 180);
  return 50 + 50 * Math.sin(delta * Math.PI / 180);
}

const tp={}; const trackH=15;
function setupTP(){tp.r=trackH/2;tp.lenStraight=W-2*M-60;tp.s1=tp.lenStraight;tp.s2=Math.PI*tp.r;tp.s3=tp.lenStraight;tp.s4=Math.PI*tp.r;tp.per=tp.s1+tp.s2+tp.s3+tp.s4;}
setupTP();

const p1={x:mapX(X_MIN),y:mapY(37)}, p2={x:mapX(X_MAX),y:mapY(63)};
const CX=(p1.x+p2.x)/2, CY=(p1.y+p2.y)/2; const ALPHA=Math.atan2(p2.y-p1.y,p2.x-p1.x);
function localToWorld(px,py){const c=Math.cos(ALPHA),s=Math.sin(ALPHA);return{x:CX+px*c-py*s,y:CY+px*s+py*c};}
function trackLocalPos(r){let d=(r*tp.per)%tp.per;if(d<0)d+=tp.per;const xs=-tp.lenStraight/2,xe=tp.lenStraight/2,r0=tp.r; if(d<=tp.s1)return{x:xs+d,y:-r0}; if(d<=tp.s1+tp.s2){const dd=d-tp.s1,a=-Math.PI/2+dd/r0;return{x:xe+r0*Math.cos(a),y:r0*Math.sin(a)}} if(d<=tp.s1+tp.s2+tp.s3){const dd=d-tp.s1-tp.s2;return{x:xe-dd,y:r0};} const dd=d-tp.s1-tp.s2-tp.s3,a=Math.PI/2+dd/r0;return{x:xs+r0*Math.cos(a),y:r0*Math.sin(a)}}
const trackPos=r=>localToWorld(...Object.values(trackLocalPos(r)));
function LsToTrackRatio(LsDeg) { return ((LsDeg - 315 + 360) % 360) / 360; }

function drawAxes(){
  ctx.strokeStyle='#aaa';ctx.lineWidth=1;
  ctx.beginPath();ctx.moveTo(M,M);ctx.lineTo(M,H-M);ctx.lineTo(W-M,H-M);ctx.stroke();
  ctx.fillStyle='#fff';ctx.font='bold 12px sans-serif';ctx.textAlign='center';
  ctx.fillText('太阳赤纬 (°)',W/2,H-6);
  // x轴范围标注
  ctx.textBaseline='top';
  ctx.textAlign='left'; ctx.fillText('-23.44°', M+10, H-M+2);
  ctx.textAlign='right'; ctx.fillText('+23.44°', W-M-30, H-M+2);
  // y轴范围标注
  ctx.textAlign='right'; ctx.fillText('100%', M-8, M+5);
  ctx.textAlign='right'; ctx.fillText('0%', M-8, H-M);
// 跑道包络极值点采样法：找y最大和最小点
  let maxY = -Infinity, minY = Infinity;
  let maxPt = null, minPt = null;
  const N = 300;
  for (let i = 0; i < N; i++) {
    const t = i / (N - 1);
    const pt = trackPos(t);
    if (pt.y > maxY) { maxY = pt.y; maxPt = pt; }
    if (pt.y < minY) { minY = pt.y; minPt = pt; }
  }
// 跑道区间标示（黄色，与交点严格对齐）
  ctx.save();
  ctx.globalAlpha = 0.45;
  ctx.fillStyle = '#ff0'; // 黄色
  ctx.fillRect(M-2, maxPt.y, 4, minPt.y - maxPt.y);
  ctx.restore();
// 跑道交点标示
  ctx.save();
  ctx.strokeStyle = '#ff0';
  ctx.lineWidth = 1.5;
  ctx.setLineDash([5, 5]);
  ctx.beginPath();
  ctx.moveTo(minPt.x, minPt.y);
  ctx.lineTo(M, minPt.y);
  ctx.moveTo(maxPt.x, maxPt.y);
  ctx.lineTo(M, maxPt.y);
  ctx.stroke();
  ctx.setLineDash([]);
  // 在交点处标注百分比
  ctx.fillStyle = '#ff0';
  ctx.font = 'bold 12px sans-serif';
  ctx.textAlign = 'right';
  ctx.textBaseline = 'middle';
  ctx.fillText('37%', M - 10, maxPt.y);
  ctx.fillText('63%', M - 10, minPt.y);
  ctx.restore();
// y轴主标题（进一步下移，彻底避免与数值重叠）
  ctx.save();ctx.translate(12,H/2+150);ctx.rotate(-Math.PI/2);ctx.textAlign='center'; ctx.fillText('北半球阳照面积 (%)',0,0);ctx.restore();
}

function drawTrack(){ctx.save();ctx.translate(CX,CY);ctx.rotate(ALPHA);const xs=-tp.lenStraight/2,xe=tp.lenStraight/2,r=tp.r;ctx.strokeStyle='#666';ctx.lineWidth=1.5;ctx.beginPath();ctx.moveTo(xs,-r);ctx.lineTo(xe,-r);ctx.arc(xe,0,r,-Math.PI/2,Math.PI/2,false);ctx.lineTo(xs,r);ctx.arc(xs,0,r,Math.PI/2,3*Math.PI/2,false);ctx.stroke();ctx.restore();
  ctx.save();ctx.translate(CX,CY);ctx.rotate(ALPHA);
  ctx.beginPath();ctx.strokeStyle='#44f';ctx.lineWidth=2;
  for (let i = 0; i <= 360; i++) {
    const Ls = (Ls0 + i) % 360;
    const r = LsToTrackRatio(Ls);
    const base = trackLocalPos(r);
    const offset = (insolationFraction(Ls) - 50) * 0.05;
    const px = base.x, py = base.y - offset;
    if (i === 0) ctx.moveTo(px, py); else ctx.lineTo(px, py);
  }
  ctx.stroke();ctx.restore();
}

function drawTerms(){
  ctx.font='10px sans-serif';ctx.fillStyle='#fff';ctx.textBaseline='middle';
  const upper=[],lower=[];
  termNames.forEach((name,i)=>{
    const Ls=sunLonByTerm(i);
    const r=LsToTrackRatio(Ls);
    const pos=trackPos(r);
    const isUp=trackLocalPos(r).y<0;
    (isUp?upper:lower).push({name,x:pos.x,y:pos.y,Ls});
  });
  upper.sort((a,b)=>a.x-b.x);
  lower.sort((a,b)=>a.x-b.x);
  const GAP_UP=22,GAP_LOW=26;
  upper.forEach((o,idx)=>plotLabel(o,o.y-(20+idx*GAP_UP),true));
  lower.forEach((o,idx)=>plotLabel(o,o.y+(20+idx*GAP_LOW),false));
  // 输出左下角节气信息
  if(lower.length>0){
    let leftBottom=lower.reduce((acc,cur)=>{
      if(cur.x<acc.x||(cur.x===acc.x&&cur.y>acc.y))return cur; else return acc;
    },lower[0]);
// 输出所有下方节气的分布
//   lower.forEach(o=>drawPoint(o, o.name));
  }
// 绘制当前节气
  function plotLabel(o,ly,isUp){ctx.strokeStyle='#777';ctx.lineWidth=1;ctx.beginPath();ctx.moveTo(o.x,o.y);ctx.lineTo(o.x,o.y+(isUp?-5:5));ctx.lineTo(o.x,ly);ctx.stroke();ctx.textAlign='center';ctx.fillText(`${o.name}(${term2month[o.name]}月)`,o.x,ly);}
  function drawPoint(pt,name){ctx.fillStyle='#ff0';ctx.beginPath();ctx.arc(pt.x,pt.y,4,0,Math.PI*2);ctx.fill();ctx.font='bold 12px sans-serif';ctx.fillStyle='#fff';ctx.textAlign='left';ctx.textBaseline='bottom';ctx.fillText(`${name}(${term2month[name]}月)`,pt.x+8,pt.y-6);}
}

// 全局函数 drawPoint：在轨道图上绘制节气点，供 animate 调用
function drawPoint(pt, name) {
  ctx.fillStyle = '#ff0';
  ctx.beginPath();
  ctx.arc(pt.x, pt.y, 4, 0, Math.PI * 2);
  ctx.fill();
  ctx.font = 'bold 12px sans-serif';
  ctx.fillStyle = '#fff';
  ctx.textAlign = 'left';
  ctx.textBaseline = 'bottom';
  ctx.fillText(`${name}(${term2month[name]}月)`, pt.x + 8, pt.y - 6);
}

const clock=new THREE.Clock();
function animate(){
  const t=clock.getElapsedTime(); const orbital=(t/YEAR_S*2*Math.PI)%(2*Math.PI);
  const LsDeg=(Ls0+THREE.MathUtils.radToDeg(orbital))%360;
  const r = LsToTrackRatio(LsDeg);
  const earthPos=new THREE.Vector3(Math.cos(orbital)*ORBIT_R,0,Math.sin(orbital)*ORBIT_R);
  earthOrbitG.position.copy(earthPos);
  conn.geometry.setFromPoints([sunMesh.position.clone(),earthPos.clone()]);conn.computeLineDistances();
  const bigPos=new THREE.Vector3();bigEarth.getWorldPosition(bigPos); leftSunDir.position.copy(bigPos.clone().add(new THREE.Vector3().subVectors(earthPos,sunMesh.position).normalize().multiplyScalar(ORBIT_R*2)));
  leftSunDir.target.position.copy(bigPos); leftSunDir.target.updateMatrixWorld();
  const daily=(t/DAY_S*2*Math.PI)%(2*Math.PI); earthMesh.rotation.y=daily; bigEarth.rotation.y=daily;
  controls.update(); renderer.render(scene,camera);
  ctx.clearRect(0,0,W,H);
// ==== 新增：北纬45°白昼时长 ====  
// 1. 计算太阳赤纬
  const OBLIQ = 23.44 * Math.PI / 180; // 地轴倾角（弧度）
  const LsRad = LsDeg * Math.PI / 180; // 太阳黄经（弧度）
  const delta = Math.asin(Math.sin(OBLIQ) * Math.sin(LsRad)); // 太阳赤纬（弧度）
// 2. 计算北纬45°白昼时长
  const phi = 45 * Math.PI / 180; // 45°纬度
  let h0 = -Math.tan(phi) * Math.tan(delta);
  h0 = Math.max(-1, Math.min(1, h0)); // 防止超出arccos定义域
  const dayLength = (24/Math.PI) * Math.acos(h0); // 小时
// 3. 绘制文本
  ctx.save();
  ctx.font = 'bold 18px sans-serif';
  ctx.textAlign = 'center';
  ctx.fillStyle = '#fff';
  ctx.fillText(`北纬45°白昼时长：${dayLength.toFixed(2)} 小时`, W/2, 32);
  ctx.restore();
// 绘制图表
  ctx.save();
  ctx.translate(0,25); // 整体下移25像素
  drawAxes(); drawTrack(); drawTerms();
  const pt = trackPos(r);
  const idx=termIndexBySunLon(LsDeg); drawPoint(pt,termNames[idx]);
  ctx.restore();
  labelRenderer.render(scene, camera);
  requestAnimationFrame(animate);}



addEventListener('resize',()=>{camera.aspect=innerWidth/innerHeight;camera.updateProjectionMatrix();renderer.setSize(innerWidth,innerHeight);W=chart.width;H=chart.height;setupTP();});
animate();
</script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的数据库写入示例 - 不依赖mysql-connector-python
使用subprocess调用mysql命令行工具
"""

import subprocess
import json
import os

def execute_sql_command(sql_command, database=None):
    """执行SQL命令"""
    try:
        cmd = ['mysql', '-u', 'root']
        if database:
            cmd.extend(['-D', database])
        
        # 使用stdin传递SQL命令
        result = subprocess.run(
            cmd,
            input=sql_command,
            text=True,
            capture_output=True,
            check=True
        )
        
        print(f"✅ SQL执行成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ SQL执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def setup_database():
    """初始化数据库和表结构"""
    print("🚀 正在设置数据库...")
    
    # 1. 创建数据库
    create_db_sql = """
    CREATE DATABASE IF NOT EXISTS tech_knowledge_base CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    """
    
    if execute_sql_command(create_db_sql):
        print("✅ 数据库创建成功")
    else:
        print("❌ 数据库创建失败")
        return False
    
    # 2. 创建表结构
    create_tables_sql = """
    USE tech_knowledge_base;
    
    -- 知识分类表
    CREATE TABLE IF NOT EXISTS knowledge_categories (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        parent_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES knowledge_categories(id) ON DELETE SET NULL,
        INDEX idx_parent_id (parent_id)
    );
    
    -- 知识条目主表
    CREATE TABLE IF NOT EXISTS knowledge_entries (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        summary TEXT,
        content LONGTEXT NOT NULL,
        category_id INT,
        difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'intermediate',
        tags JSON,
        source_type ENUM('ai_explanation', 'code_example', 'documentation', 'tutorial') DEFAULT 'ai_explanation',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES knowledge_categories(id) ON DELETE SET NULL,
        FULLTEXT KEY ft_content (title, summary, content),
        INDEX idx_category (category_id),
        INDEX idx_difficulty (difficulty_level),
        INDEX idx_source_type (source_type)
    );
    
    -- 代码示例表
    CREATE TABLE IF NOT EXISTS code_examples (
        id INT PRIMARY KEY AUTO_INCREMENT,
        knowledge_entry_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        language VARCHAR(50) NOT NULL,
        code_content LONGTEXT NOT NULL,
        file_path VARCHAR(500),
        line_start INT,
        line_end INT,
        is_executable BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (knowledge_entry_id) REFERENCES knowledge_entries(id) ON DELETE CASCADE,
        INDEX idx_knowledge_entry (knowledge_entry_id),
        INDEX idx_language (language)
    );
    """
    
    if execute_sql_command(create_tables_sql):
        print("✅ 表结构创建成功")
        return True
    else:
        print("❌ 表结构创建失败")
        return False

def insert_sample_data():
    """插入示例数据"""
    print("📝 正在插入示例数据...")
    
    # 1. 插入分类
    categories_sql = """
    USE tech_knowledge_base;
    
    INSERT IGNORE INTO knowledge_categories (name, description) VALUES
    ('Metal编程', 'Apple Metal图形编程相关知识'),
    ('Swift开发', 'Swift语言和iOS/macOS开发'),
    ('3D图形学', '3D图形学基础概念和技术');
    """
    
    if not execute_sql_command(categories_sql):
        print("❌ 分类插入失败")
        return False
    
    # 2. 插入知识条目
    knowledge_sql = """
    USE tech_knowledge_base;
    
    INSERT INTO knowledge_entries (title, summary, content, category_id, tags, difficulty_level) 
    SELECT 
        'Metal坐标系基础',
        '介绍Metal中3D坐标系的基本概念和变换原理',
        '# Metal坐标系基础\\n\\n## 什么是坐标系？\\n\\n在3D图形编程中，坐标系用于定义物体在3D空间中的位置。Metal使用右手坐标系。\\n\\n## 主要坐标系类型\\n\\n1. **本地坐标系** - 每个3D对象自己的坐标系\\n2. **世界坐标系** - 整个3D场景的统一坐标系\\n3. **视图坐标系** - 从相机视角看到的坐标系\\n4. **屏幕坐标系** - 最终显示在屏幕上的2D坐标\\n\\n## NDC坐标范围\\n\\n- X轴: -1.0 到 +1.0\\n- Y轴: -1.0 到 +1.0\\n- Z轴: 0.0 到 1.0',
        kc.id,
        '[\"坐标系\", \"3D变换\", \"NDC\", \"Metal\"]',
        'beginner'
    FROM knowledge_categories kc 
    WHERE kc.name = 'Metal编程'
    LIMIT 1;
    """
    
    if not execute_sql_command(knowledge_sql):
        print("❌ 知识条目插入失败")
        return False
    
    # 3. 插入代码示例
    code_sql = """
    USE tech_knowledge_base;
    
    INSERT INTO code_examples (knowledge_entry_id, title, description, language, code_content)
    SELECT 
        ke.id,
        '基础坐标变换着色器',
        'Metal着色器中实现基础的3D坐标变换',
        'metal',
        'vertex VertexOut basic_vertex(Vertex in [[stage_in]],\\n                               constant float4x4& mvpMatrix [[buffer(1)]]) {\\n    VertexOut out;\\n    out.position = mvpMatrix * float4(in.position, 1.0);\\n    return out;\\n}'
    FROM knowledge_entries ke 
    WHERE ke.title = 'Metal坐标系基础'
    LIMIT 1;
    """
    
    if execute_sql_command(code_sql):
        print("✅ 示例数据插入成功")
        return True
    else:
        print("❌ 代码示例插入失败")
        return False

def query_data():
    """查询数据验证插入是否成功"""
    print("🔍 正在验证数据...")
    
    query_sql = """
    USE tech_knowledge_base;
    
    SELECT 
        ke.id,
        ke.title,
        kc.name as category_name,
        ke.tags,
        COUNT(ce.id) as code_examples_count
    FROM knowledge_entries ke
    LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
    LEFT JOIN code_examples ce ON ke.id = ce.knowledge_entry_id
    GROUP BY ke.id, ke.title, kc.name, ke.tags;
    """
    
    execute_sql_command(query_sql)

if __name__ == "__main__":
    print("🗄️  开始数据库写入演示...")
    
    # 1. 设置数据库
    if setup_database():
        # 2. 插入示例数据
        if insert_sample_data():
            # 3. 验证数据
            query_data()
            print("\\n✨ 数据库写入演示完成！")
            print("\\n📖 你可以使用以下方式查看数据:")
            print("1. 直接使用MySQL命令: mysql -u root tech_knowledge_base")
            print("2. 运行web应用: python knowledge_web_app.py")
        else:
            print("❌ 数据插入失败")
    else:
        print("❌ 数据库设置失败")
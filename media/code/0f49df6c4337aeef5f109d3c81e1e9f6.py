#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库访问
"""

import subprocess
import sys

def test_mysql_command():
    """测试通过命令行访问MySQL"""
    print("=== 测试命令行MySQL访问 ===")
    
    try:
        # 查询知识条目
        cmd = ['mysql', '-u', 'root', 'tech_knowledge_base', '-e', 
               "SELECT id, title, summary, difficulty_level FROM knowledge_entries;"]
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        print("✅ 数据库连接成功！")
        print("\n📊 知识条目列表：")
        print(result.stdout)
        
        # 查询分类
        cmd2 = ['mysql', '-u', 'root', 'tech_knowledge_base', '-e', 
                "SELECT id, name, description FROM knowledge_categories;"]
        
        result2 = subprocess.run(cmd2, capture_output=True, text=True, check=True)
        print("\n📂 知识分类：")
        print(result2.stdout)
        
        # 查询代码示例
        cmd3 = ['mysql', '-u', 'root', 'tech_knowledge_base', '-e', 
                "SELECT id, knowledge_entry_id, title, language FROM code_examples;"]
        
        result3 = subprocess.run(cmd3, capture_output=True, text=True, check=True)
        print("\n💻 代码示例：")
        print(result3.stdout)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库连接失败: {e}")
        print(f"错误信息: {e.stderr}")
        return False

def test_python_connector():
    """测试Python连接器"""
    print("\n=== 测试Python mysql-connector ===")
    
    try:
        import mysql.connector
        
        # 连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor(dictionary=True)
        
        # 查询数据
        cursor.execute("SELECT COUNT(*) as total FROM knowledge_entries")
        count = cursor.fetchone()
        print(f"✅ Python连接成功！知识条目总数: {count['total']}")
        
        # 获取一条详细记录
        cursor.execute("""
            SELECT ke.id, ke.title, ke.summary, kc.name as category_name 
            FROM knowledge_entries ke 
            LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id 
            LIMIT 1
        """)
        
        entry = cursor.fetchone()
        if entry:
            print(f"\n📖 示例条目:")
            print(f"ID: {entry['id']}")
            print(f"标题: {entry['title']}")
            print(f"分类: {entry['category_name']}")
            print(f"摘要: {entry['summary'][:100]}..." if entry['summary'] else "无摘要")
        
        cursor.close()
        conn.close()
        
        return True
        
    except ImportError:
        print("❌ mysql-connector-python 未安装")
        print("可以通过以下命令安装: pip install mysql-connector-python")
        return False
    except Exception as e:
        print(f"❌ Python连接失败: {e}")
        return False

def main():
    print("🗄️  测试数据库访问...")
    
    # 测试命令行方式
    cmd_success = test_mysql_command()
    
    # 测试Python连接器
    py_success = test_python_connector()
    
    print(f"\n📊 测试结果:")
    print(f"命令行访问: {'✅ 成功' if cmd_success else '❌ 失败'}")
    print(f"Python连接: {'✅ 成功' if py_success else '❌ 失败'}")
    
    if cmd_success or py_success:
        print("\n🎉 您的内存数据库可以正常访问！")
        print("\n📋 可用的操作:")
        print("1. 通过命令行查询: mysql -u root tech_knowledge_base")
        print("2. 通过Python脚本操作数据")
        print("3. 运行Web应用查看界面")
    else:
        print("\n❌ 数据库访问失败，需要检查配置")

if __name__ == "__main__":
    main()
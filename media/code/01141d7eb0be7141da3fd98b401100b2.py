#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库写入完整指南
展示各种向数据库写入数据的方法
"""

import subprocess
import json
import os
from datetime import datetime

class SimpleDatabaseManager:
    """简单的数据库管理器，使用MySQL命令行工具"""
    
    def __init__(self, database='tech_knowledge_base'):
        self.database = database
    
    def execute_sql(self, sql_command):
        """执行SQL命令"""
        try:
            cmd = ['mysql', '-u', 'root', '-D', self.database]
            result = subprocess.run(
                cmd,
                input=sql_command,
                text=True,
                capture_output=True,
                check=True
            )
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            return False, e.stderr
    
    def insert_knowledge_entry(self, title, content, summary="", category="", tags=None, difficulty="intermediate"):
        """插入知识条目"""
        if tags is None:
            tags = []
        
        # 转义SQL字符串
        title = title.replace("'", "''")
        content = content.replace("'", "''")
        summary = summary.replace("'", "''")
        tags_json = json.dumps(tags, ensure_ascii=False).replace("'", "''")
        
        # 获取分类ID
        category_id = "NULL"
        if category:
            category_sql = f"SELECT id FROM knowledge_categories WHERE name = '{category}' LIMIT 1;"
            success, result = self.execute_sql(category_sql)
            if success and result.strip():
                category_id = result.strip().split('\\n')[-1]
        
        # 插入知识条目
        insert_sql = f"""
        INSERT INTO knowledge_entries (title, summary, content, category_id, tags, difficulty_level) 
        VALUES ('{title}', '{summary}', '{content}', {category_id}, '{tags_json}', '{difficulty}');
        
        SELECT LAST_INSERT_ID();
        """
        
        success, result = self.execute_sql(insert_sql)
        if success:
            knowledge_id = result.strip().split('\\n')[-1]
            print(f"✅ 知识条目插入成功，ID: {knowledge_id}")
            return int(knowledge_id)
        else:
            print(f"❌ 知识条目插入失败: {result}")
            return None
    
    def insert_code_example(self, knowledge_id, title, language, code_content, description=""):
        """插入代码示例"""
        title = title.replace("'", "''")
        code_content = code_content.replace("'", "''")
        description = description.replace("'", "''")
        
        insert_sql = f"""
        INSERT INTO code_examples (knowledge_entry_id, title, description, language, code_content)
        VALUES ({knowledge_id}, '{title}', '{description}', '{language}', '{code_content}');
        """
        
        success, result = self.execute_sql(insert_sql)
        if success:
            print(f"✅ 代码示例插入成功")
            return True
        else:
            print(f"❌ 代码示例插入失败: {result}")
            return False

def demo_insert_swift_knowledge():
    """演示：插入Swift相关知识"""
    db = SimpleDatabaseManager()
    
    print("📱 插入Swift编程知识...")
    
    # 1. 插入Swift基础知识
    knowledge_id = db.insert_knowledge_entry(
        title="Swift可选类型详解",
        summary="深入理解Swift中的可选类型(Optional)，包括声明、解包和最佳实践",
        content="""# Swift可选类型详解

## 什么是可选类型？

可选类型是Swift中处理值可能不存在情况的安全方式。用`?`声明。

```swift
var name: String?  // 可选字符串，可能是字符串也可能是nil
```

## 可选类型的用途

1. **处理可能失败的操作**
   - 数据转换失败
   - 数组越界访问
   - 字典取值可能为空

2. **初始化延迟**
   - 属性可能在初始化时没有值
   - 避免强制提供初始值

## 解包方式

### 1. 强制解包 (!)
```swift
let unwrapped = name!  // 危险！如果name是nil会崩溃
```

### 2. 可选绑定 (if let / guard let)
```swift
if let unwrappedName = name {
    print("姓名是: \\(unwrappedName)")
} else {
    print("没有姓名")
}
```

### 3. 空合并运算符 (??)
```swift
let displayName = name ?? "未知用户"
```

## 最佳实践

1. **避免强制解包** - 除非100%确定有值
2. **使用可选绑定** - 安全的解包方式
3. **提供默认值** - 使用??运算符
4. **可选链调用** - 使用?.安全调用方法""",
        category="Swift开发",
        tags=["Optional", "可选类型", "解包", "安全编程", "Swift"],
        difficulty="beginner"
    )
    
    if knowledge_id:
        # 2. 添加代码示例
        db.insert_code_example(
            knowledge_id=knowledge_id,
            title="可选类型的正确使用",
            language="swift",
            code_content="""import Foundation

// 1. 声明可选类型
var userAge: Int?
var userName: String?

// 2. 赋值
userAge = 25
userName = "张三"

// 3. 安全解包 - 可选绑定
if let age = userAge, let name = userName {
    print("用户 \\(name) 的年龄是 \\(age) 岁")
} else {
    print("用户信息不完整")
}

// 4. 空合并运算符
let displayAge = userAge ?? 0
let displayName = userName ?? "匿名用户"
print("显示: \\(displayName), 年龄: \\(displayAge)")

// 5. 可选链调用
struct Person {
    var name: String
    var pet: Pet?
}

struct Pet {
    var name: String
    var age: Int
}

let person = Person(name: "李四", pet: Pet(name: "小白", age: 3))

// 安全访问嵌套属性
if let petAge = person.pet?.age {
    print("宠物年龄: \\(petAge)")
}

// 6. guard语句的使用
func processUser(age: Int?, name: String?) {
    guard let validAge = age, let validName = name else {
        print("参数无效")
        return
    }
    
    print("处理用户: \\(validName), 年龄: \\(validAge)")
}""",
            description="展示Swift可选类型的各种使用方法和最佳实践"
        )
    
    return knowledge_id

def demo_insert_xcode_tips():
    """演示：插入Xcode使用技巧"""
    db = SimpleDatabaseManager()
    
    print("🛠️ 插入Xcode使用技巧...")
    
    knowledge_id = db.insert_knowledge_entry(
        title="Xcode调试技巧大全",
        summary="提升开发效率的Xcode调试技巧，包括断点、控制台命令、视图调试等",
        content="""# Xcode调试技巧大全

## 断点使用技巧

### 1. 条件断点
- 右键断点 → Edit Breakpoint
- 添加条件：`index > 10`
- 只在满足条件时暂停

### 2. 日志断点
- 设置Action为"Log Message"
- 输出变量值：`Value is @index@`
- 不暂停程序继续运行

### 3. 符号断点
- ⌘+8 → + → Symbolic Breakpoint
- 函数名：`viewDidLoad`
- 在所有类的viewDidLoad处暂停

## 控制台调试命令

### LLDB常用命令
```
po object          // 打印对象描述
p variable         // 打印变量值
expr variable = 5  // 修改变量值
bt                 // 查看调用栈
frame variable     // 查看当前帧的所有变量
```

### 内存调试
```
memory read 0x...  // 读取内存地址
image list         // 查看加载的动态库
thread list        // 查看所有线程
```

## 视图调试

### View Debugger
- 运行时点击"Debug View Hierarchy"
- 3D查看视图层次结构
- 检查约束问题
- 查看视图属性

### Accessibility Inspector
- 检查无障碍访问
- 测试VoiceOver支持
- 验证控件标签

## 性能调试

### Instruments工具
1. **Time Profiler** - CPU使用分析
2. **Allocations** - 内存分配跟踪
3. **Leaks** - 内存泄漏检测
4. **Core Animation** - 动画性能

### Xcode内置工具
- Debug Navigator中的CPU/内存监控
- 网络使用情况
- 磁盘读写统计

## 编译优化

### Build Settings
- Debug配置：优化关闭，符号信息完整
- Release配置：开启优化，去除调试信息

### 编译时间优化
```swift
// 减少编译时间的技巧
1. 明确类型声明：let name: String = "test"
2. 减少复杂的类型推断
3. 分解复杂表达式
4. 使用@objc减少Swift桥接
```""",
        category="Swift开发",
        tags=["Xcode", "调试", "断点", "LLDB", "性能优化", "开发工具"],
        difficulty="intermediate"
    )
    
    if knowledge_id:
        # 添加LLDB命令示例
        db.insert_code_example(
            knowledge_id=knowledge_id,
            title="LLDB调试命令实例",
            language="lldb",
            code_content="""# 在断点处执行的LLDB命令示例

# 1. 查看变量值
(lldb) po self
(lldb) p count
(lldb) frame variable

# 2. 修改变量值
(lldb) expr count = 100
(lldb) expr self.title = "新标题"

# 3. 调用方法
(lldb) po [self description]
(lldb) expr self.updateUI()

# 4. 查看对象类型
(lldb) po object_getClass(self)
(lldb) image lookup -t MyClass

# 5. 内存相关
(lldb) memory read -s 8 -c 4 0x...
(lldb) malloc_info --stack-history 0x...

# 6. 线程调试
(lldb) thread list
(lldb) thread select 2
(lldb) bt all

# 7. 设置条件断点
(lldb) breakpoint set -n viewDidLoad -c 'index > 5'
(lldb) breakpoint modify -c 'name != nil' 1

# 8. 查看调用栈
(lldb) bt
(lldb) up
(lldb) down
(lldb) frame select 3""",
            description="实用的LLDB调试命令，帮助快速定位和解决问题"
        )
    
    return knowledge_id

def demo_query_inserted_data():
    """演示：查询已插入的数据"""
    db = SimpleDatabaseManager()
    
    print("🔍 查询已插入的数据...")
    
    # 查询所有知识条目
    query_sql = """
    SELECT 
        ke.id,
        ke.title,
        ke.difficulty_level,
        kc.name as category_name,
        ke.tags,
        ke.created_at,
        COUNT(ce.id) as code_examples_count
    FROM knowledge_entries ke
    LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
    LEFT JOIN code_examples ce ON ke.id = ce.knowledge_entry_id
    GROUP BY ke.id
    ORDER BY ke.created_at DESC;
    """
    
    success, result = db.execute_sql(query_sql)
    if success:
        print("📚 知识库内容:")
        print(result)
    else:
        print(f"❌ 查询失败: {result}")

if __name__ == "__main__":
    print("🗄️  数据库写入完整演示...")
    
    # 1. 插入Swift知识
    swift_id = demo_insert_swift_knowledge()
    
    # 2. 插入Xcode技巧
    xcode_id = demo_insert_xcode_tips()
    
    # 3. 查询所有数据
    demo_query_inserted_data()
    
    print("\\n✨ 演示完成！")
    print("\\n🌐 启动Web应用查看完整效果:")
    print("   python knowledge_web_app.py")
    print("   然后访问 http://localhost:8080")
    
    print("\\n📊 或者直接使用MySQL查看:")
    print("   mysql -u root tech_knowledge_base")
    print("   SELECT * FROM knowledge_entries;")
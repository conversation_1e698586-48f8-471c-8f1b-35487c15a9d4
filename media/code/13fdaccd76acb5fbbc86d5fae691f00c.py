#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向技术知识数据库写入数据的示例
展示如何使用KnowledgeManager类保存各种类型的技术知识
"""

from knowledge_manager import KnowledgeManager

def example_save_knowledge():
    """示例：保存一个完整的技术知识条目"""
    
    # 1. 创建KnowledgeManager实例
    km = KnowledgeManager(
        host='localhost',
        user='root', 
        password='',  # 你的MySQL密码
        database='tech_knowledge_base'
    )
    
    try:
        # 2. 保存主要知识条目
        knowledge_id = km.save_knowledge_entry(
            title="Swift中的内存管理",
            summary="详解Swift的ARC机制、强引用循环和解决方案",
            content="""# Swift内存管理详解

## ARC (Automatic Reference Counting)

Swift使用自动引用计数来管理内存，当对象的引用计数为0时自动释放内存。

## 强引用循环问题

当两个对象相互强引用时，会导致内存泄漏：

```swift
class Person {
    var pet: Pet?
}

class Pet {
    var owner: Person?  // 这里会形成强引用循环
}
```

## 解决方案

1. **弱引用 (weak)**：用于可选类型的引用
2. **无主引用 (unowned)**：用于非可选类型的引用

正确的实现：
```swift
class Pet {
    weak var owner: Person?  // 使用weak避免循环引用
}
```""",
            category_name="Swift开发",
            tags=["内存管理", "ARC", "强引用循环", "weak", "unowned"],
            difficulty="intermediate",
            source_type="ai_explanation"
        )
        
        print(f"✅ 保存知识条目成功，ID: {knowledge_id}")
        
        # 3. 保存代码示例
        code_id = km.save_code_example(
            knowledge_entry_id=knowledge_id,
            title="弱引用解决循环引用",
            language="swift",
            code_content="""class Person {
    let name: String
    var pet: Pet?
    
    init(name: String) {
        self.name = name
    }
    
    deinit {
        print("\\(name) 被释放")
    }
}

class Pet {
    let name: String
    weak var owner: Person?  // 使用weak避免循环引用
    
    init(name: String) {
        self.name = name
    }
    
    deinit {
        print("宠物 \\(name) 被释放")
    }
}

// 使用示例
var person: Person? = Person(name: "张三")
var pet: Pet? = Pet(name: "小白")

person?.pet = pet
pet?.owner = person

// 设置为nil时，两个对象都会被正确释放
person = nil
pet = nil""",
            description="展示如何使用weak引用解决Swift中的强引用循环问题"
        )
        
        print(f"✅ 保存代码示例成功，ID: {code_id}")
        
        # 4. 保存图表演示
        diagram_id = km.save_diagram(
            knowledge_entry_id=knowledge_id,
            title="Swift内存管理流程",
            diagram_type="mermaid",
            diagram_definition="""graph TD
    A[创建对象] --> B[引用计数+1]
    B --> C[对象被引用]
    C --> D[引用计数+1]
    C --> E[引用被移除]
    E --> F[引用计数-1]
    F --> G{引用计数=0?}
    G -->|是| H[自动释放内存]
    G -->|否| I[继续存在]
    
    style A fill:#e1f5fe
    style H fill:#e8f5e8
    style G fill:#fff3e0""",
            description="展示Swift ARC的工作流程"
        )
        
        print(f"✅ 保存图表成功，ID: {diagram_id}")
        
        return knowledge_id
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return None
    finally:
        km.close()

def example_save_simple_knowledge():
    """示例：快速保存简单的技术知识"""
    
    km = KnowledgeManager()
    
    try:
        # 保存一个简单的知识点
        knowledge_id = km.save_knowledge_entry(
            title="Xcode快捷键大全",
            content="""# Xcode常用快捷键

## 编辑相关
- `Cmd + /` - 注释/取消注释
- `Cmd + Shift + O` - 快速打开文件
- `Cmd + Ctrl + E` - 重命名
- `Cmd + Opt + [` 或 `]` - 上移/下移代码行

## 调试相关  
- `Cmd + R` - 运行项目
- `Cmd + .` - 停止运行
- `F8` - 单步调试
- `Cmd + Y` - 启用/禁用断点

## 导航相关
- `Cmd + Shift + J` - 在导航器中显示当前文件
- `Cmd + Ctrl + ←/→` - 前进/后退
- `Cmd + Shift + Y` - 显示/隐藏控制台""",
            category_name="Swift开发",
            tags=["Xcode", "快捷键", "效率"],
            difficulty="beginner"
        )
        
        print(f"✅ 保存Xcode快捷键知识，ID: {knowledge_id}")
        return knowledge_id
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return None
    finally:
        km.close()

def example_search_knowledge():
    """示例：搜索已保存的知识"""
    
    km = KnowledgeManager()
    
    try:
        # 1. 全文搜索
        results = km.search_knowledge("内存管理", search_type="fulltext")
        print(f"🔍 搜索'内存管理'找到 {len(results)} 条结果:")
        for result in results:
            print(f"  - {result['title']}")
        
        # 2. 标签搜索
        results = km.search_knowledge("", search_type="tag", tags=["Swift"])
        print(f"\n🏷️  标签搜索'Swift'找到 {len(results)} 条结果")
        
        # 3. 分类搜索
        results = km.search_knowledge("", search_type="category", category="Swift开发")
        print(f"\n📂 分类搜索'Swift开发'找到 {len(results)} 条结果")
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
    finally:
        km.close()

def example_get_complete_knowledge():
    """示例：获取完整的知识条目（包含代码和图表）"""
    
    km = KnowledgeManager()
    
    try:
        # 获取ID为1的知识条目
        knowledge = km.get_knowledge_with_examples(1)
        
        if knowledge:
            print(f"📚 知识标题: {knowledge['title']}")
            print(f"📝 摘要: {knowledge['summary']}")
            print(f"🏷️  标签: {knowledge['tags']}")
            print(f"💻 代码示例数量: {len(knowledge['code_examples'])}")
            print(f"📊 图表数量: {len(knowledge['diagrams'])}")
        else:
            print("❌ 未找到指定的知识条目")
            
    except Exception as e:
        print(f"❌ 获取失败: {e}")
    finally:
        km.close()

if __name__ == "__main__":
    print("🚀 开始演示数据库写入操作...")
    
    # 保存复杂的知识条目
    print("\n1. 保存完整的技术知识:")
    knowledge_id1 = example_save_knowledge()
    
    # 保存简单的知识条目
    print("\n2. 保存简单的知识点:")
    knowledge_id2 = example_save_simple_knowledge()
    
    # 搜索知识
    print("\n3. 搜索已保存的知识:")
    example_search_knowledge()
    
    # 获取完整知识条目
    print("\n4. 获取完整的知识条目:")
    example_get_complete_knowledge()
    
    print("\n✨ 数据库操作演示完成！")
    print("🌐 启动Web应用查看：python knowledge_web_app.py")
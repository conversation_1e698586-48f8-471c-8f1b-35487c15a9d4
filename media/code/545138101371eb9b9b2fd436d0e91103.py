#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多媒体知识管理系统
支持语音、视频、图片等高维度知识内容
"""

import mysql.connector
import json
import hashlib
import os
import mimetypes
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import base64

# 尝试导入语音处理库
try:
    import speech_recognition as sr
    import pydub
    AUDIO_SUPPORT = True
except ImportError:
    AUDIO_SUPPORT = False
    print("⚠️  语音处理库未安装，音频功能将受限")

try:
    import cv2
    import pytesseract
    IMAGE_SUPPORT = True
except ImportError:
    IMAGE_SUPPORT = False
    print("⚠️  图像处理库未安装，图像功能将受限")

class MultimediaKnowledgeManager:
    def __init__(self, host='localhost', user='root', password='', database='tech_knowledge_base'):
        """初始化多媒体知识管理器"""
        self.connection = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True
        )
        self.cursor = self.connection.cursor(dictionary=True)
        
        # 创建媒体存储目录
        self.media_root = "media"
        self.audio_dir = os.path.join(self.media_root, "audio")
        self.video_dir = os.path.join(self.media_root, "video")
        self.image_dir = os.path.join(self.media_root, "images")
        
        for directory in [self.media_root, self.audio_dir, self.video_dir, self.image_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def save_media_file(self, file_path: str, original_name: str = "", description: str = "") -> int:
        """保存媒体文件到系统"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 计算文件哈希
        file_hash = self._calculate_file_hash(file_path)
        
        # 检查是否已存在
        self.cursor.execute("SELECT id FROM media_files WHERE file_hash = %s", (file_hash,))
        existing = self.cursor.fetchone()
        if existing:
            print(f"文件已存在，ID: {existing['id']}")
            return existing['id']
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        mime_type, _ = mimetypes.guess_type(file_path)
        media_type = self._determine_media_type(mime_type)
        
        # 生成新文件名和路径
        file_ext = os.path.splitext(file_path)[1]
        new_filename = f"{file_hash}{file_ext}"
        
        if media_type == 'audio':
            new_path = os.path.join(self.audio_dir, new_filename)
        elif media_type == 'video':
            new_path = os.path.join(self.video_dir, new_filename)
        elif media_type == 'image':
            new_path = os.path.join(self.image_dir, new_filename)
        else:
            new_path = os.path.join(self.media_root, new_filename)
        
        # 复制文件
        import shutil
        shutil.copy2(file_path, new_path)
        
        # 获取媒体元数据
        metadata = self._extract_media_metadata(new_path, media_type)
        
        # 保存到数据库
        query = """
        INSERT INTO media_files 
        (filename, original_name, file_path, file_size, mime_type, media_type, 
         duration_seconds, file_hash, metadata)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        self.cursor.execute(query, (
            new_filename,
            original_name or os.path.basename(file_path),
            new_path,
            file_size,
            mime_type or 'application/octet-stream',
            media_type,
            metadata.get('duration'),
            file_hash,
            json.dumps(metadata, ensure_ascii=False)
        ))
        
        media_id = self.cursor.lastrowid
        
        # 如果是音频文件，尝试转录
        if media_type == 'audio' and AUDIO_SUPPORT:
            self._transcribe_audio(media_id, new_path)
        
        # 如果是图片文件，尝试OCR
        if media_type == 'image' and IMAGE_SUPPORT:
            self._extract_text_from_image(media_id, new_path)
        
        return media_id
    
    def attach_media_to_knowledge(self, knowledge_id: int, media_id: int, 
                                 role: str = 'attachment', description: str = "") -> int:
        """将媒体文件关联到知识条目"""
        query = """
        INSERT INTO knowledge_media 
        (knowledge_entry_id, media_file_id, media_role, description)
        VALUES (%s, %s, %s, %s)
        """
        
        self.cursor.execute(query, (knowledge_id, media_id, role, description))
        return self.cursor.lastrowid
    
    def save_knowledge_with_audio(self, title: str, content: str, audio_file_path: str,
                                 summary: str = "", category_name: str = "", 
                                 tags: List[str] = None) -> Tuple[int, int]:
        """保存包含音频的知识条目"""
        # 保存知识条目
        from knowledge_manager import KnowledgeManager
        km = KnowledgeManager()
        knowledge_id = km.save_knowledge_entry(
            title=title, content=content, summary=summary,
            category_name=category_name, tags=tags or []
        )
        km.close()
        
        # 保存音频文件
        media_id = self.save_media_file(audio_file_path, f"{title}_audio.mp3")
        
        # 关联音频到知识条目
        self.attach_media_to_knowledge(knowledge_id, media_id, 'main_audio', '主要音频解释')
        
        return knowledge_id, media_id
    
    def search_multimedia_knowledge(self, query: str, include_audio: bool = True,
                                   include_video: bool = True) -> List[Dict]:
        """多媒体智能搜索"""
        try:
            # 使用存储过程进行智能搜索
            self.cursor.callproc('SmartSearch', [query, include_audio, include_video, 20])
            
            results = []
            for result in self.cursor.stored_results():
                results.extend(result.fetchall())
            
            return results
        except:
            # 如果存储过程不可用，使用基本搜索
            return self._basic_multimedia_search(query)
    
    def get_knowledge_with_media(self, knowledge_id: int) -> Dict:
        """获取包含媒体的完整知识条目"""
        # 获取基本知识信息
        query = """
        SELECT ke.*, kc.name as category_name
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        WHERE ke.id = %s
        """
        self.cursor.execute(query, (knowledge_id,))
        entry = self.cursor.fetchone()
        
        if not entry:
            return {}
        
        # 获取媒体文件
        query = """
        SELECT mf.*, km.media_role, km.description as role_description
        FROM media_files mf
        JOIN knowledge_media km ON mf.id = km.media_file_id
        WHERE km.knowledge_entry_id = %s
        ORDER BY km.display_order, km.id
        """
        self.cursor.execute(query, (knowledge_id,))
        entry['media_files'] = self.cursor.fetchall()
        
        # 获取音频转录
        for media in entry['media_files']:
            if media['media_type'] == 'audio':
                query = "SELECT * FROM speech_transcriptions WHERE media_file_id = %s"
                self.cursor.execute(query, (media['id'],))
                media['transcription'] = self.cursor.fetchone()
        
        # 获取代码示例
        query = "SELECT * FROM code_examples WHERE knowledge_entry_id = %s ORDER BY id"
        self.cursor.execute(query, (knowledge_id,))
        entry['code_examples'] = self.cursor.fetchall()
        
        # 获取图表
        query = "SELECT * FROM diagrams WHERE knowledge_entry_id = %s ORDER BY id"
        self.cursor.execute(query, (knowledge_id,))
        entry['diagrams'] = self.cursor.fetchall()
        
        return entry
    
    def add_audio_annotation(self, media_id: int, start_time: float, end_time: float,
                           annotation: str, annotation_type: str = 'note') -> int:
        """为音频添加时间点注释"""
        query = """
        INSERT INTO audio_annotations 
        (media_file_id, start_time, end_time, annotation_text, annotation_type)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        self.cursor.execute(query, (media_id, start_time, end_time, annotation, annotation_type))
        return self.cursor.lastrowid
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _determine_media_type(self, mime_type: str) -> str:
        """根据MIME类型确定媒体类型"""
        if not mime_type:
            return 'document'
        
        if mime_type.startswith('audio/'):
            return 'audio'
        elif mime_type.startswith('video/'):
            return 'video'
        elif mime_type.startswith('image/'):
            return 'image'
        else:
            return 'document'
    
    def _extract_media_metadata(self, file_path: str, media_type: str) -> Dict:
        """提取媒体文件元数据"""
        metadata = {}
        
        try:
            if media_type == 'audio' and AUDIO_SUPPORT:
                audio = pydub.AudioSegment.from_file(file_path)
                metadata.update({
                    'duration': len(audio) / 1000.0,  # 转换为秒
                    'channels': audio.channels,
                    'sample_rate': audio.frame_rate,
                    'bitrate': audio.frame_rate * audio.frame_width * 8 * audio.channels
                })
            
            elif media_type == 'video' and IMAGE_SUPPORT:
                cap = cv2.VideoCapture(file_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    
                    metadata.update({
                        'duration': frame_count / fps if fps > 0 else 0,
                        'fps': fps,
                        'resolution': f"{width}x{height}",
                        'frame_count': frame_count
                    })
                cap.release()
            
            elif media_type == 'image' and IMAGE_SUPPORT:
                img = cv2.imread(file_path)
                if img is not None:
                    height, width, channels = img.shape
                    metadata.update({
                        'resolution': f"{width}x{height}",
                        'channels': channels
                    })
        
        except Exception as e:
            print(f"提取元数据失败: {e}")
        
        return metadata
    
    def _transcribe_audio(self, media_id: int, file_path: str):
        """转录音频文件"""
        if not AUDIO_SUPPORT:
            return
        
        try:
            # 转换音频格式
            audio = pydub.AudioSegment.from_file(file_path)
            wav_path = file_path.replace(os.path.splitext(file_path)[1], '.wav')
            audio.export(wav_path, format="wav")
            
            # 语音识别
            r = sr.Recognizer()
            with sr.AudioFile(wav_path) as source:
                audio_data = r.record(source)
                text = r.recognize_google(audio_data, language='zh-CN')
            
            # 保存转录结果
            query = """
            INSERT INTO speech_transcriptions 
            (media_file_id, transcription_text, language, transcription_engine)
            VALUES (%s, %s, %s, %s)
            """
            
            self.cursor.execute(query, (media_id, text, 'zh-CN', 'google'))
            
            # 清理临时文件
            if os.path.exists(wav_path) and wav_path != file_path:
                os.remove(wav_path)
            
            print(f"✅ 音频转录完成: {text[:50]}...")
            
        except Exception as e:
            print(f"❌ 音频转录失败: {e}")
    
    def _extract_text_from_image(self, media_id: int, file_path: str):
        """从图片中提取文字"""
        if not IMAGE_SUPPORT:
            return
        
        try:
            # OCR文字识别
            text = pytesseract.image_to_string(file_path, lang='chi_sim+eng')
            
            if text.strip():
                # 保存到搜索索引
                query = """
                INSERT INTO knowledge_search_index 
                (knowledge_entry_id, content_type, searchable_content)
                SELECT km.knowledge_entry_id, 'image_ocr', %s
                FROM knowledge_media km 
                WHERE km.media_file_id = %s
                """
                
                self.cursor.execute(query, (text, media_id))
                print(f"✅ 图片文字提取完成: {text[:50]}...")
        
        except Exception as e:
            print(f"❌ 图片文字提取失败: {e}")
    
    def _basic_multimedia_search(self, query: str) -> List[Dict]:
        """基本多媒体搜索"""
        search_pattern = f"%{query}%"
        sql = """
        SELECT DISTINCT ke.*, kc.name as category_name,
               GROUP_CONCAT(DISTINCT mf.media_type) as available_media
        FROM knowledge_entries ke
        LEFT JOIN knowledge_categories kc ON ke.category_id = kc.id
        LEFT JOIN knowledge_media km ON ke.id = km.knowledge_entry_id
        LEFT JOIN media_files mf ON km.media_file_id = mf.id
        LEFT JOIN speech_transcriptions st ON mf.id = st.media_file_id
        WHERE ke.title LIKE %s OR ke.summary LIKE %s OR ke.content LIKE %s
           OR st.transcription_text LIKE %s
        GROUP BY ke.id
        ORDER BY ke.updated_at DESC
        LIMIT 20
        """
        
        self.cursor.execute(sql, (search_pattern, search_pattern, search_pattern, search_pattern))
        return self.cursor.fetchall()
    
    def close(self):
        """关闭数据库连接"""
        self.cursor.close()
        self.connection.close()


# 使用示例
def demo_multimedia_knowledge():
    """演示多媒体知识管理"""
    mkm = MultimediaKnowledgeManager()
    
    print("🎵 多媒体知识管理系统演示")
    print("支持的功能:")
    print("✅ 音频文件存储和转录")
    print("✅ 视频文件存储和元数据提取")
    print("✅ 图片文件存储和OCR文字识别")
    print("✅ 多维度智能搜索")
    print("✅ 音频时间点注释")
    print("✅ 知识关联图谱")
    
    mkm.close()

if __name__ == "__main__":
    demo_multimedia_knowledge()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示音频文件
使用系统的文字转语音功能生成Metal深度测试的语音解释
"""

import os
import subprocess
import sys

def create_demo_audio_macos():
    """在macOS上使用say命令创建演示音频"""
    
    # Metal深度测试的解释文本
    text = """
    深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。
    
    当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。
    
    Metal使用深度缓冲区来存储每个像素的深度值，深度值范围从0.0到1.0，
    其中0.0表示最近的像素，1.0表示最远的像素。
    
    深度测试的工作原理是：当渲染新像素时，GPU会比较新像素的深度值与深度缓冲区中已存储的深度值。
    如果新像素更近，则更新颜色缓冲区和深度缓冲区；如果新像素更远，则丢弃该像素。
    
    这种机制确保了正确的3D视觉效果，让近处的对象能够正确遮挡远处的对象。
    
    在我们的太阳系应用中，深度测试确保地球在太阳前面时能正确遮挡太阳，
    在太阳后面时被太阳遮挡，创造出真实的3D效果。
    """
    
    # 输出文件路径
    output_path = "media/audio/demo_audio.mp3"
    temp_aiff_path = "media/audio/temp_demo.aiff"
    
    try:
        print("🎵 正在生成演示音频...")
        
        # 使用macOS的say命令生成语音（输出为AIFF格式）
        cmd = [
            'say', 
            '-v', 'Ting-Ting',  # 使用中文语音
            '-o', temp_aiff_path,
            text
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ say命令失败: {result.stderr}")
            return False
        
        print("✅ 语音生成完成")
        
        # 检查是否有ffmpeg来转换格式
        try:
            # 尝试使用ffmpeg转换为MP3
            ffmpeg_cmd = [
                'ffmpeg', '-i', temp_aiff_path, 
                '-acodec', 'mp3', '-ab', '128k',
                '-y', output_path
            ]
            
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 音频转换为MP3格式完成")
                # 删除临时文件
                os.remove(temp_aiff_path)
            else:
                print("⚠️  ffmpeg转换失败，保留AIFF格式")
                # 重命名AIFF文件
                os.rename(temp_aiff_path, output_path.replace('.mp3', '.aiff'))
                output_path = output_path.replace('.mp3', '.aiff')
        
        except FileNotFoundError:
            print("⚠️  未找到ffmpeg，保留AIFF格式")
            # 重命名AIFF文件
            os.rename(temp_aiff_path, output_path.replace('.mp3', '.aiff'))
            output_path = output_path.replace('.mp3', '.aiff')
        
        # 检查文件是否创建成功
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ 音频文件创建成功: {output_path}")
            print(f"📊 文件大小: {file_size} 字节")
            return output_path
        else:
            print("❌ 音频文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建音频失败: {e}")
        return False

def create_simple_audio_file():
    """创建一个简单的音频文件占位符"""
    output_path = "media/audio/demo_audio.mp3"
    
    # 创建一个简单的文本文件作为占位符
    placeholder_text = """
这是一个音频文件占位符。

实际的音频内容：
深度测试是3D图形渲染中的核心技术，用于确定哪些像素应该被渲染到屏幕上。
当多个3D对象重叠时，GPU需要知道哪个对象在前面，哪个在后面。

要听到真实的语音，您可以：
1. 安装语音合成软件
2. 录制自己的语音解释
3. 使用在线文字转语音服务

转录文本已保存在数据库中，您可以在知识详情页面查看。
"""
    
    # 创建文本文件
    text_path = output_path.replace('.mp3', '.txt')
    with open(text_path, 'w', encoding='utf-8') as f:
        f.write(placeholder_text)
    
    print(f"✅ 创建了文本占位符: {text_path}")
    return text_path

def update_database_path(actual_path):
    """更新数据库中的文件路径"""
    import mysql.connector
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='tech_knowledge_base',
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        
        # 获取文件信息
        file_size = os.path.getsize(actual_path)
        mime_type = 'audio/mpeg' if actual_path.endswith('.mp3') else 'audio/aiff' if actual_path.endswith('.aiff') else 'text/plain'
        
        # 更新数据库记录
        update_query = """
        UPDATE media_files 
        SET file_path = %s, file_size = %s, mime_type = %s
        WHERE id = 1
        """
        
        cursor.execute(update_query, (actual_path, file_size, mime_type))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        print("✅ 数据库路径已更新")
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🎵 创建演示音频文件")
    print("=" * 40)
    
    # 检查操作系统
    if sys.platform == 'darwin':  # macOS
        print("🍎 检测到macOS系统，使用say命令生成语音")
        audio_path = create_demo_audio_macos()
    else:
        print("🖥️  非macOS系统，创建文本占位符")
        audio_path = create_simple_audio_file()
    
    if audio_path:
        # 更新数据库
        if update_database_path(audio_path):
            print("\n🎉 演示音频设置完成！")
            print("🌐 现在可以在Web界面中查看音频内容")
            print("📖 访问 http://localhost:8081 并点击深度测试知识条目")
        else:
            print("\n⚠️  音频文件已创建，但数据库更新失败")
    else:
        print("\n❌ 音频文件创建失败")
    
    print("\n💡 提示：")
    print("- 如果没有声音，可以录制自己的语音解释")
    print("- 可以使用在线TTS服务生成语音")
    print("- 语音转录文本已保存，可以在界面中查看")

if __name__ == "__main__":
    main()

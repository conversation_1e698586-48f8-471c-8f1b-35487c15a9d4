#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件夹结构上传器 - 保留完整目录结构
支持递归上传文件夹，保持原有的目录层次结构
"""

import os
import requests
import json
from pathlib import Path
import mimetypes

def upload_file_with_folder_path(file_path, folder_path, base_url="http://localhost:8082"):
    """
    上传单个文件，保留文件夹路径信息
    
    Args:
        file_path: 文件的完整路径
        folder_path: 相对于根目录的文件夹路径
        base_url: 服务器地址
    """
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f)}
            data = {'folder_path': folder_path}
            
            response = requests.post(f"{base_url}/upload", files=files, data=data)
            
            if response.status_code == 200:
                print(f"✅ 上传成功: {folder_path}/{os.path.basename(file_path)}")
                return True
            else:
                print(f"❌ 上传失败: {folder_path}/{os.path.basename(file_path)} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传错误: {folder_path}/{os.path.basename(file_path)} - {str(e)}")
        return False

def upload_folder_structure(root_folder, base_url="http://localhost:8082"):
    """
    递归上传文件夹，保留完整的目录结构
    
    Args:
        root_folder: 要上传的根文件夹路径
        base_url: 服务器地址
    """
    root_path = Path(root_folder)
    if not root_path.exists() or not root_path.is_dir():
        print(f"❌ 文件夹不存在: {root_folder}")
        return
    
    print(f"🚀 开始上传文件夹: {root_folder}")
    print(f"📡 服务器地址: {base_url}")
    
    success_count = 0
    error_count = 0
    total_files = 0
    
    # 统计总文件数
    for root, dirs, files in os.walk(root_path):
        total_files += len(files)
    
    print(f"📊 发现 {total_files} 个文件")
    
    # 递归上传所有文件
    for root, dirs, files in os.walk(root_path):
        current_path = Path(root)
        
        # 计算相对路径
        relative_path = current_path.relative_to(root_path)
        folder_path = str(relative_path) if str(relative_path) != '.' else ''
        
        print(f"\n📂 处理文件夹: {folder_path or '根目录'}")
        
        for file in files:
            file_path = current_path / file
            
            # 跳过隐藏文件和系统文件
            if file.startswith('.') or file in ['Thumbs.db', 'desktop.ini']:
                print(f"⏭️  跳过系统文件: {file}")
                continue
            
            # 上传文件
            if upload_file_with_folder_path(str(file_path), folder_path, base_url):
                success_count += 1
            else:
                error_count += 1
            
            # 显示进度
            processed = success_count + error_count
            progress = (processed / total_files) * 100
            print(f"📈 进度: {processed}/{total_files} ({progress:.1f}%)")
    
    print(f"\n🎉 上传完成!")
    print(f"✅ 成功: {success_count} 个文件")
    print(f"❌ 失败: {error_count} 个文件")
    print(f"📊 总计: {success_count + error_count} 个文件")

def main():
    """主函数"""
    print("📁 文件夹结构上传器")
    print("=" * 50)
    
    # 获取用户输入
    folder_path = input("请输入要上传的文件夹路径: ").strip()
    
    if not folder_path:
        print("❌ 请提供有效的文件夹路径")
        return
    
    # 确认上传
    print(f"\n📂 准备上传文件夹: {folder_path}")
    confirm = input("确认上传? (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("❌ 取消上传")
        return
    
    # 开始上传
    upload_folder_structure(folder_path)

if __name__ == "__main__":
    main()
